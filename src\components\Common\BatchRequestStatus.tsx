import React from 'react';
import { AlertCircle, CheckCircle, Clock, RefreshCw } from 'lucide-react';

interface BatchRequestStatusProps {
  /** 总股票数量 */
  totalCount: number;
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failedCount: number;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 是否正在刷新 */
  isFetching: boolean;
  /** 错误信息 */
  error?: Error | null;
  /** 重试函数 */
  onRetry?: () => void;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 批量请求状态显示组件
 * 用于显示批量股票数据请求的状态和结果
 */
export const BatchRequestStatus: React.FC<BatchRequestStatusProps> = ({
  totalCount,
  successCount,
  failedCount,
  isLoading,
  isFetching,
  error,
  onRetry,
  showDetails = true,
  className = '',
}) => {
  // 计算成功率
  const successRate = totalCount > 0 ? (successCount / totalCount) * 100 : 0;
  
  // 确定状态
  const getStatus = () => {
    if (isLoading) return 'loading';
    if (error) return 'error';
    if (failedCount > 0 && successCount === 0) return 'failed';
    if (failedCount > 0) return 'partial';
    if (successCount > 0) return 'success';
    return 'idle';
  };

  const status = getStatus();

  // 状态配置
  const statusConfig = {
    loading: {
      icon: Clock,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      message: '正在加载股票数据...'
    },
    error: {
      icon: AlertCircle,
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      message: '加载失败'
    },
    failed: {
      icon: AlertCircle,
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      message: '所有股票数据加载失败'
    },
    partial: {
      icon: AlertCircle,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      message: '部分股票数据加载失败'
    },
    success: {
      icon: CheckCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      message: '所有股票数据加载成功'
    },
    idle: {
      icon: Clock,
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      message: '等待加载'
    }
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  if (!showDetails && status === 'success') {
    return null; // 成功时不显示状态
  }

  return (
    <div className={`rounded-lg border p-3 ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Icon className={`h-4 w-4 ${config.color}`} />
          <span className={`text-sm font-medium ${config.color}`}>
            {config.message}
          </span>
          {isFetching && !isLoading && (
            <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />
          )}
        </div>
        
        {onRetry && (status === 'error' || status === 'failed' || status === 'partial') && (
          <button
            onClick={onRetry}
            className="text-xs px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          >
            重试
          </button>
        )}
      </div>

      {showDetails && totalCount > 0 && (
        <div className="mt-2 space-y-1">
          {/* 进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                status === 'success' ? 'bg-green-500' :
                status === 'partial' ? 'bg-yellow-500' :
                status === 'failed' ? 'bg-red-500' :
                'bg-blue-500'
              }`}
              style={{ width: `${Math.max(successRate, isLoading ? 30 : 0)}%` }}
            />
          </div>

          {/* 详细统计 */}
          <div className="flex justify-between text-xs text-gray-600">
            <span>总计: {totalCount}</span>
            <span>成功: {successCount}</span>
            {failedCount > 0 && <span className="text-red-600">失败: {failedCount}</span>}
            <span>成功率: {successRate.toFixed(1)}%</span>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="text-xs text-red-600 mt-1">
              错误: {error.message}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BatchRequestStatus;
