# -*- coding: utf-8 -*-
# Stock Fund Flow App - Quick Deployment Configuration Script
# Run this script to configure Cloudflare deployment

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Stock Fund Flow App - Deployment Configuration Wizard" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Check necessary tools
Write-Host "`nChecking necessary tools..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "[OK] Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Node.js not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check npm
try {
    $npmVersion = npm --version
    Write-Host "[OK] npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] npm not found." -ForegroundColor Red
    exit 1
}

Write-Host "`nConfiguration Steps Guide:" -ForegroundColor Cyan
Write-Host "1. Create Cloudflare KV namespaces"
Write-Host "2. Configure GitHub Secrets"
Write-Host "3. Update configuration files"
Write-Host "4. Deploy application"

Write-Host "`nPlease follow these steps:" -ForegroundColor Yellow

Write-Host "`nStep 1: Create KV Namespaces:" -ForegroundColor Cyan
Write-Host "   Visit: https://dash.cloudflare.com"
Write-Host "   Navigate: Workers & Pages -> KV"
Write-Host "   Create the following namespaces:"
Write-Host "   - gupiao-stock-cache-prod"
Write-Host "   - gupiao-stock-config-prod"
Write-Host "   - gupiao-stock-cache-preview"
Write-Host "   - gupiao-stock-config-preview"

Write-Host "`nStep 2: Get Cloudflare Information:" -ForegroundColor Cyan
Write-Host "   API Token: https://dash.cloudflare.com/profile/api-tokens"
Write-Host "   Account ID: Found in Dashboard sidebar"

Write-Host "`nStep 3: Configure GitHub Secrets:" -ForegroundColor Cyan
$repoUrl = git config --get remote.origin.url
if ($repoUrl) {
    $repoUrl = $repoUrl -replace "\.git$", ""
    $repoUrl = $repoUrl -replace "git@github\.com:", "https://github.com/"
    Write-Host "   Visit: $repoUrl/settings/secrets/actions"
} else {
    Write-Host "   Visit: https://github.com/YOUR_USERNAME/gupiao_zijinliu/settings/secrets/actions"
}

Write-Host "   Add the following Secrets:"
Write-Host "   - CLOUDFLARE_API_TOKEN"
Write-Host "   - CLOUDFLARE_ACCOUNT_ID"
Write-Host "   - VITE_API_BASE_URL"
Write-Host "   - WORKERS_URL"
Write-Host "   - FRONTEND_URL"

Write-Host "`nStep 4: Update Configuration Files:" -ForegroundColor Cyan
Write-Host "   Edit workers/wrangler.toml - Update KV namespace IDs"
Write-Host "   Edit .env.production - Update API URL"

Write-Host "`nStep 5: Deploy Application:" -ForegroundColor Cyan
Write-Host "   git add ."
Write-Host "   git commit -m 'fix: Configure Workers deployment and CORS settings'"
Write-Host "   git push origin main"

Write-Host "`nDetailed Documentation:" -ForegroundColor Green
Write-Host "   See: docs/CORS_DEPLOYMENT_GUIDE.md"

Write-Host "`nTest After Deployment:" -ForegroundColor Magenta
Write-Host "   Frontend: https://sto-fund.pages.dev"
Write-Host "   API Health Check: https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev/health"
Write-Host "   API Documentation: https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev/api/docs"

Write-Host "`nConfiguration wizard completed! Please follow the steps above." -ForegroundColor Green

# Ask whether to open related pages
Write-Host "`nWould you like to open Cloudflare Dashboard? (y/n): " -NoNewline -ForegroundColor Yellow
$openPages = Read-Host
if ($openPages -eq "y" -or $openPages -eq "Y") {
    Start-Process "https://dash.cloudflare.com"
}

Write-Host "Would you like to open the deployment guide? (y/n): " -NoNewline -ForegroundColor Yellow
$openDocs = Read-Host
if ($openDocs -eq "y" -or $openDocs -eq "Y") {
    Start-Process "docs/CORS_DEPLOYMENT_GUIDE.md"
}

Write-Host "`nReminder: After completing configuration, push code to main branch for automatic deployment!" -ForegroundColor Yellow
