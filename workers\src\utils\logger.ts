/**
 * 日志记录工具
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  source?: string;
  requestId?: string;
}

/**
 * 日志记录器类
 */
export class Logger {
  private level: LogLevel;
  private source: string;
  private requestId?: string;

  constructor(source: string = 'Worker', level: LogLevel = LogLevel.INFO) {
    this.source = source;
    this.level = level;
  }

  /**
   * 设置请求ID
   */
  setRequestId(requestId: string) {
    this.requestId = requestId;
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel) {
    this.level = level;
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(level: LogLevel, message: string, data?: any): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      source: this.source,
      requestId: this.requestId,
    };
  }

  /**
   * 格式化日志输出
   */
  private formatLog(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const requestPart = entry.requestId ? ` [${entry.requestId}]` : '';
    const dataPart = entry.data ? ` ${JSON.stringify(entry.data)}` : '';
    
    return `[${entry.timestamp}] ${levelName} [${entry.source}]${requestPart}: ${entry.message}${dataPart}`;
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, data?: any) {
    if (level < this.level) {
      return;
    }

    const entry = this.createLogEntry(level, message, data);
    const formattedLog = this.formatLog(entry);

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedLog);
        break;
      case LogLevel.INFO:
        console.info(formattedLog);
        break;
      case LogLevel.WARN:
        console.warn(formattedLog);
        break;
      case LogLevel.ERROR:
        console.error(formattedLog);
        break;
    }
  }

  /**
   * 调试日志
   */
  debug(message: string, data?: any) {
    this.log(LogLevel.DEBUG, message, data);
  }

  /**
   * 信息日志
   */
  info(message: string, data?: any) {
    this.log(LogLevel.INFO, message, data);
  }

  /**
   * 警告日志
   */
  warn(message: string, data?: any) {
    this.log(LogLevel.WARN, message, data);
  }

  /**
   * 错误日志
   */
  error(message: string, data?: any) {
    this.log(LogLevel.ERROR, message, data);
  }

  /**
   * 性能计时开始
   */
  time(label: string) {
    console.time(`[${this.source}] ${label}`);
  }

  /**
   * 性能计时结束
   */
  timeEnd(label: string) {
    console.timeEnd(`[${this.source}] ${label}`);
  }
}

/**
 * 创建日志记录器实例
 */
export function createLogger(source: string, env?: any): Logger {
  const logLevel = env?.LOG_LEVEL || 'info';
  const level = LogLevel[logLevel.toUpperCase() as keyof typeof LogLevel] || LogLevel.INFO;
  
  return new Logger(source, level);
}

/**
 * 默认日志记录器
 */
export const logger = new Logger('Default');

/**
 * 缓存统计日志
 */
export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

/**
 * 缓存统计记录器
 */
export class CacheStatsLogger {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
  };

  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * 记录缓存命中
   */
  hit(key: string) {
    this.stats.hits++;
    this.logger.debug('Cache hit', { key, stats: this.stats });
  }

  /**
   * 记录缓存未命中
   */
  miss(key: string) {
    this.stats.misses++;
    this.logger.debug('Cache miss', { key, stats: this.stats });
  }

  /**
   * 记录缓存设置
   */
  set(key: string, ttl?: number) {
    this.stats.sets++;
    this.logger.debug('Cache set', { key, ttl, stats: this.stats });
  }

  /**
   * 记录缓存删除
   */
  delete(key: string) {
    this.stats.deletes++;
    this.logger.debug('Cache delete', { key, stats: this.stats });
  }

  /**
   * 记录缓存错误
   */
  error(key: string, error: Error) {
    this.stats.errors++;
    this.logger.error('Cache error', { key, error: error.message, stats: this.stats });
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  reset() {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
    };
    this.logger.info('Cache stats reset');
  }

  /**
   * 记录统计摘要
   */
  logSummary() {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total * 100).toFixed(2) : '0.00';
    
    this.logger.info('Cache statistics summary', {
      ...this.stats,
      total,
      hitRate: `${hitRate}%`,
    });
  }
}
