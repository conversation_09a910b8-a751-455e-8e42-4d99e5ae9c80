import { EastmoneyApiService } from '../services/eastmoneyApi';

/**
 * API服务测试函数
 */
export async function testEastmoneyApi(): Promise<void> {
  console.log('开始测试东方财富API服务...');
  
  const apiService = new EastmoneyApiService();
  
  try {
    // 测试单个股票数据获取
    console.log('\n1. 测试单个股票数据获取 (600121)...');
    const singleResult = await apiService.getStockFlowData('600121', 10);
    
    if (singleResult.success) {
      console.log('✅ 单个股票数据获取成功');
      console.log('数据摘要:', JSON.stringify(singleResult.data?.summary, null, 2));
      console.log('K线数据条数:', singleResult.data?.totalCount);
    } else {
      console.log('❌ 单个股票数据获取失败:', singleResult.message);
    }
    
    // 测试批量股票数据获取
    console.log('\n2. 测试批量股票数据获取...');
    const batchResult = await apiService.getBatchFlowData(['600121', '000001', '300001'], 5);
    
    if (batchResult.success) {
      console.log('✅ 批量股票数据获取成功');
      console.log('获取到的股票数量:', Object.keys(batchResult.data || {}).length);
      
      for (const [code, data] of Object.entries(batchResult.data || {})) {
        console.log(`  ${code}: ${data.summary?.name || '未知'}`);
      }
    } else {
      console.log('❌ 批量股票数据获取失败:', batchResult.message);
    }
    
    // 测试服务状态
    console.log('\n3. 测试服务状态...');
    const status = apiService.getServiceStatus();
    console.log('服务状态:', JSON.stringify(status, null, 2));
    
    console.log('\n✅ API服务测试完成');
    
  } catch (error) {
    console.error('❌ API服务测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof globalThis !== 'undefined' && globalThis.addEventListener) {
  // 在Cloudflare Workers环境中
  globalThis.addEventListener('fetch', async (event: any) => {
    if (event.request.url.includes('/test')) {
      event.respondWith(
        (async () => {
          await testEastmoneyApi();
          return new Response('API测试完成，请查看控制台输出', {
            headers: { 'Content-Type': 'text/plain; charset=utf-8' }
          });
        })()
      );
    }
  });
}
