# 股票拖拽排序功能说明

## 功能概述

为股票实时监控功能新增了拖拽排序功能，用户可以通过鼠标拖拽来重新排列股票的显示顺序。

## 主要特性

### 1. 拖拽排序
- **拖拽手柄**：每个股票卡片左侧显示拖拽手柄图标（⋮⋮）
- **拖拽操作**：点击并按住拖拽手柄，可以拖动股票卡片到新位置
- **视觉反馈**：拖拽时卡片会显示阴影和缩放效果，提供直观的视觉反馈

### 2. 持久化存储
- **本地存储**：用户调整的顺序自动保存到本地存储（localStorage）
- **云端同步**：如果启用了云端存储，排序信息也会同步到云端
- **跨设备一致**：在不同设备间保持一致的排序顺序

### 3. 移动端支持
- **触摸拖拽**：支持触屏设备的触摸拖拽操作
- **响应式设计**：在不同屏幕尺寸下都能正常工作

## 技术实现

### 使用的库
- `@dnd-kit/core`：核心拖拽功能
- `@dnd-kit/sortable`：排序功能
- `@dnd-kit/utilities`：工具函数

### 核心组件更新

#### 1. 类型定义更新
```typescript
// src/types/index.ts
export interface Stock {
  code: string;
  name: string;
  sortOrder?: number; // 新增：排序位置
}

// src/types/stock.ts
export interface StockInfo {
  code: string;
  name: string;
  addedAt: string;
  sortOrder?: number; // 新增：排序位置
}
```

#### 2. useStockList Hook 更新
- 新增 `reorderStocks` 函数用于处理拖拽排序
- 更新数据加载逻辑，确保股票按 `sortOrder` 排序
- 更新云端同步逻辑，包含 `sortOrder` 字段

#### 3. StockList 组件更新
- 集成 `@dnd-kit` 拖拽功能
- 新增 `SortableStockListItem` 组件
- 添加拖拽手柄和视觉反馈

## 使用方法

### 基本操作
1. **添加股票**：使用股票输入框添加股票代码
2. **拖拽排序**：点击并按住股票卡片左侧的拖拽手柄（⋮⋮）
3. **拖动到目标位置**：将股票拖动到想要的位置
4. **释放鼠标**：释放鼠标完成排序

### 排序规则
- 新添加的股票会自动排在列表末尾
- 拖拽后的顺序会立即保存
- 下次打开应用时会保持上次的排序顺序

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 设备支持
- 桌面端：鼠标拖拽
- 移动端：触摸拖拽
- 平板端：触摸拖拽

## 注意事项

1. **拖拽激活距离**：需要拖动至少 8px 才会激活拖拽功能，避免误触
2. **键盘支持**：支持键盘操作进行排序（Tab + 空格键 + 方向键）
3. **实时数据更新**：拖拽排序不会影响股票数据的实时更新功能
4. **云端同步**：如果网络不稳定，排序信息可能需要一些时间才能同步到云端

## 开发说明

### 安装依赖
```bash
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
```

### 关键代码文件
- `src/types/index.ts` - 类型定义
- `src/types/stock.ts` - 股票相关类型
- `src/hooks/useStockList.ts` - 股票列表管理 Hook
- `src/components/StockManager/StockList.tsx` - 股票列表组件
- `src/components/StockManager/index.tsx` - 股票管理主组件

### 测试建议
1. 添加多个股票代码
2. 测试拖拽排序功能
3. 刷新页面验证排序是否保持
4. 在不同设备上测试触摸拖拽
5. 测试云端同步功能（如果启用）
