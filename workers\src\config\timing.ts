/**
 * Workers 时间配置
 * 与前端配置保持同步的后端时间配置
 * 
 * <AUTHOR> System
 * @version 1.0.0
 * @date 2025-01-02
 */

/**
 * API调用延迟配置
 */
export const API_DELAYS = {
  /** API请求之间的延迟（毫秒） - 从200ms调整为1000ms */
  BETWEEN_REQUESTS: 1000,
  /** 频率限制器检查间隔（毫秒） */
  RATE_LIMITER_CHECK: 100,
  /** 重试基础延迟（毫秒） */
  RETRY_BASE: 1000,
  /** 重试最大延迟（毫秒） */
  RETRY_MAX: 30000,
  /** 自适应延迟初始值（毫秒） */
  ADAPTIVE_INITIAL: 1000,
  /** 自适应延迟最大值（毫秒） */
  ADAPTIVE_MAX: 10000,
} as const;

/**
 * 频率限制配置
 */
export const RATE_LIMIT_CONFIG = {
  /** 每分钟最大请求数 */
  MAX_REQUESTS: 60,
  /** 时间窗口（毫秒） */
  WINDOW_MS: 60000,
} as const;

/**
 * 缓存TTL配置（秒）
 */
export const CACHE_TTL = {
  /** 股票数据缓存时间 */
  STOCK_DATA: 60,
  /** 最后更新时间缓存 */
  LAST_UPDATE: 300,
  /** 股票行情缓存时间 */
  STOCK_QUOTE: 30,
  /** 批量数据缓存时间 */
  BATCH_DATA: 60,
} as const;

/**
 * 创建延迟函数
 * @param ms 延迟时间（毫秒）
 * @returns Promise
 */
export function createDelay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 获取API调用间隔
 * @returns API调用间隔（毫秒）
 */
export function getApiCallInterval(): number {
  return API_DELAYS.BETWEEN_REQUESTS;
}

/**
 * 格式化时间间隔为可读字符串
 * @param ms 毫秒数
 * @returns 格式化后的字符串
 */
export function formatInterval(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60 * 1000) {
    return `${Math.round(ms / 1000)}秒`;
  } else if (ms < 60 * 60 * 1000) {
    return `${Math.round(ms / (60 * 1000))}分钟`;
  } else {
    return `${Math.round(ms / (60 * 60 * 1000))}小时`;
  }
}
