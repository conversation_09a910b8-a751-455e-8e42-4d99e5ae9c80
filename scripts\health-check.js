#!/usr/bin/env node

/**
 * 健康检查脚本
 * 用于验证部署后的服务是否正常工作
 */

const https = require('https');
const http = require('http');

// 配置
const config = {
  workers: {
    url: process.env.WORKERS_URL || 'http://127.0.0.1:8787',
    timeout: 10000,
  },
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3000',
    timeout: 10000,
  },
};

/**
 * 发送HTTP请求
 */
function makeRequest(url, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    const req = client.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers,
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * 检查Workers API
 */
async function checkWorkers() {
  console.log('🔍 检查 Workers API...');
  
  try {
    // 健康检查
    const healthResponse = await makeRequest(`${config.workers.url}/health`);
    if (healthResponse.statusCode !== 200) {
      throw new Error(`Health check failed: HTTP ${healthResponse.statusCode}`);
    }
    console.log('✅ Workers 健康检查通过');

    // API测试
    const testResponse = await makeRequest(`${config.workers.url}/api/test`);
    if (testResponse.statusCode !== 200) {
      throw new Error(`API test failed: HTTP ${testResponse.statusCode}`);
    }
    
    const testData = JSON.parse(testResponse.data);
    if (!testData.message || !testData.message.includes('API测试完成')) {
      throw new Error('API test response invalid');
    }
    console.log('✅ Workers API 功能测试通过');

    // 定时任务状态检查
    const cronResponse = await makeRequest(`${config.workers.url}/api/cron/status`);
    if (cronResponse.statusCode !== 200) {
      throw new Error(`Cron status check failed: HTTP ${cronResponse.statusCode}`);
    }
    
    const cronData = JSON.parse(cronResponse.data);
    if (!cronData.success) {
      throw new Error('Cron status check failed');
    }
    console.log('✅ Workers 定时任务状态正常');

    return true;
  } catch (error) {
    console.error('❌ Workers 检查失败:', error.message);
    return false;
  }
}

/**
 * 检查前端应用
 */
async function checkFrontend() {
  console.log('🔍 检查前端应用...');
  
  try {
    const response = await makeRequest(config.frontend.url);
    if (response.statusCode !== 200) {
      throw new Error(`Frontend check failed: HTTP ${response.statusCode}`);
    }
    
    // 检查是否包含预期的HTML内容
    if (!response.data.includes('<title>') || !response.data.includes('<div id="root">')) {
      throw new Error('Frontend HTML content invalid');
    }
    
    console.log('✅ 前端应用访问正常');
    return true;
  } catch (error) {
    console.error('❌ 前端检查失败:', error.message);
    return false;
  }
}

/**
 * 检查CORS配置
 */
async function checkCORS() {
  console.log('🔍 检查 CORS 配置...');
  
  try {
    const response = await makeRequest(`${config.workers.url}/api/test`);
    const corsHeaders = response.headers['access-control-allow-origin'];
    
    if (!corsHeaders) {
      throw new Error('CORS headers not found');
    }
    
    console.log('✅ CORS 配置正常');
    return true;
  } catch (error) {
    console.error('❌ CORS 检查失败:', error.message);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始健康检查...\n');
  
  const results = await Promise.all([
    checkWorkers(),
    checkFrontend(),
    checkCORS(),
  ]);
  
  const allPassed = results.every(result => result === true);
  
  console.log('\n📊 检查结果:');
  console.log(`Workers API: ${results[0] ? '✅' : '❌'}`);
  console.log(`前端应用: ${results[1] ? '✅' : '❌'}`);
  console.log(`CORS配置: ${results[2] ? '✅' : '❌'}`);
  
  if (allPassed) {
    console.log('\n🎉 所有检查通过！应用部署成功。');
    process.exit(0);
  } else {
    console.log('\n💥 部分检查失败，请检查部署配置。');
    process.exit(1);
  }
}

// 运行健康检查
if (require.main === module) {
  main().catch(error => {
    console.error('💥 健康检查执行失败:', error);
    process.exit(1);
  });
}

module.exports = { checkWorkers, checkFrontend, checkCORS };
