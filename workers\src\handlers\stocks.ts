import { Context } from 'hono';
import { CacheService } from '../services/cache';
import { ApiResponse, StockInfo, Env } from '../types/api';
import { validateStockCode, sanitizeStockCode } from '../services/validator';

/**
 * 股票管理API处理器
 */
export class StocksHandler {
  private cache: CacheService;

  constructor(env: Env) {
    this.cache = new CacheService(env.STOCK_CONFIG);
  }

  /**
   * 获取股票列表
   * GET /api/stocks
   */
  async getStocks(c: Context): Promise<Response> {
    try {
      const stocks = await this.cache.get<StockInfo[]>(CacheService.getStockListKey()) || [];
      
      return c.json({
        success: true,
        data: stocks,
        timestamp: new Date().toISOString(),
      } as ApiResponse<StockInfo[]>);
    } catch (error) {
      console.error('获取股票列表失败:', error);
      return c.json({
        success: false,
        message: '获取股票列表失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 添加股票
   * POST /api/stocks
   */
  async addStock(c: Context): Promise<Response> {
    try {
      const body = await c.req.json().catch(() => ({}));
      const { code, name } = body;

      // 验证请求数据
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 清洗和验证股票代码
      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 获取现有股票列表
      const existingStocks = await this.cache.get<StockInfo[]>(CacheService.getStockListKey()) || [];
      
      // 检查是否已存在
      const exists = existingStocks.some(stock => stock.code === cleanCode);
      if (exists) {
        return c.json({
          success: false,
          message: '股票代码已存在',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 409);
      }

      // 创建新股票信息
      const newStock: StockInfo = {
        code: cleanCode,
        name: name || `股票${cleanCode}`,
        addedAt: new Date().toISOString(),
      };

      // 更新股票列表
      const updatedStocks = [...existingStocks, newStock];
      await this.cache.set(CacheService.getStockListKey(), updatedStocks, 86400); // 24小时缓存

      return c.json({
        success: true,
        data: newStock,
        message: '股票添加成功',
        timestamp: new Date().toISOString(),
      } as ApiResponse<StockInfo>);
    } catch (error) {
      console.error('添加股票失败:', error);
      return c.json({
        success: false,
        message: '添加股票失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 删除股票
   * DELETE /api/stocks/:code
   */
  async deleteStock(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      
      // 获取现有股票列表
      const existingStocks = await this.cache.get<StockInfo[]>(CacheService.getStockListKey()) || [];
      
      // 检查股票是否存在
      const stockIndex = existingStocks.findIndex(stock => stock.code === cleanCode);
      if (stockIndex === -1) {
        return c.json({
          success: false,
          message: '股票代码不存在',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 404);
      }

      // 删除股票
      const deletedStock = existingStocks[stockIndex];
      const updatedStocks = existingStocks.filter(stock => stock.code !== cleanCode);
      
      // 更新缓存
      await this.cache.set(CacheService.getStockListKey(), updatedStocks, 86400);
      
      // 清理相关的数据缓存
      await this.cache.delete(CacheService.getStockDataKey(cleanCode));
      await this.cache.delete(CacheService.getLastUpdateKey(cleanCode));

      return c.json({
        success: true,
        data: deletedStock,
        message: '股票删除成功',
        timestamp: new Date().toISOString(),
      } as ApiResponse<StockInfo>);
    } catch (error) {
      console.error('删除股票失败:', error);
      return c.json({
        success: false,
        message: '删除股票失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 批量添加股票
   * POST /api/stocks/batch
   */
  async addStocksBatch(c: Context): Promise<Response> {
    try {
      const body = await c.req.json().catch(() => ({}));
      const { stocks } = body;

      if (!Array.isArray(stocks) || stocks.length === 0) {
        return c.json({
          success: false,
          message: '股票列表不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (stocks.length > 20) {
        return c.json({
          success: false,
          message: '一次最多只能添加20个股票',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const existingStocks = await this.cache.get<StockInfo[]>(CacheService.getStockListKey()) || [];
      const newStocks: StockInfo[] = [];
      const errors: string[] = [];

      for (const stockData of stocks) {
        const { code, name } = stockData;
        
        if (!code) {
          errors.push('股票代码不能为空');
          continue;
        }

        const cleanCode = sanitizeStockCode(code);
        if (!validateStockCode(cleanCode)) {
          errors.push(`无效的股票代码: ${code}`);
          continue;
        }

        // 检查是否已存在
        const exists = existingStocks.some(stock => stock.code === cleanCode) ||
                      newStocks.some(stock => stock.code === cleanCode);
        
        if (exists) {
          errors.push(`股票代码已存在: ${cleanCode}`);
          continue;
        }

        newStocks.push({
          code: cleanCode,
          name: name || `股票${cleanCode}`,
          addedAt: new Date().toISOString(),
        });
      }

      // 更新股票列表
      if (newStocks.length > 0) {
        const updatedStocks = [...existingStocks, ...newStocks];
        await this.cache.set(CacheService.getStockListKey(), updatedStocks, 86400);
      }

      return c.json({
        success: newStocks.length > 0,
        data: {
          added: newStocks,
          errors: errors,
          total: newStocks.length,
        },
        message: `成功添加 ${newStocks.length} 个股票${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('批量添加股票失败:', error);
      return c.json({
        success: false,
        message: '批量添加股票失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 清空所有股票
   * DELETE /api/stocks
   */
  async clearAllStocks(c: Context): Promise<Response> {
    try {
      const existingStocks = await this.cache.get<StockInfo[]>(CacheService.getStockListKey()) || [];
      const count = existingStocks.length;

      // 清空股票列表
      await this.cache.set(CacheService.getStockListKey(), [], 86400);

      // 清理所有相关缓存
      const deletePromises = existingStocks.map(stock => [
        this.cache.delete(CacheService.getStockDataKey(stock.code)),
        this.cache.delete(CacheService.getLastUpdateKey(stock.code)),
      ]).flat();

      await Promise.all(deletePromises);

      return c.json({
        success: true,
        data: { deletedCount: count },
        message: `成功清空 ${count} 个股票`,
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('清空股票列表失败:', error);
      return c.json({
        success: false,
        message: '清空股票列表失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }
}
