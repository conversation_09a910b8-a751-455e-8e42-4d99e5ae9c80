/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: any) => boolean;
}

/**
 * 默认重试配置
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => {
    // 默认重试条件：网络错误或5xx服务器错误
    if (error instanceof Response) {
      return error.status >= 500 || error.status === 429;
    }
    return true; // 其他错误也重试
  },
};

/**
 * 计算指数退避延迟
 * @param attempt 当前尝试次数（从0开始）
 * @param config 重试配置
 * @returns 延迟时间（毫秒）
 */
export function calculateBackoffDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
  const jitter = Math.random() * 0.1 * delay; // 添加10%的随机抖动
  return Math.min(delay + jitter, config.maxDelay);
}

/**
 * 延迟函数
 * @param ms 延迟时间（毫秒）
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带重试的异步函数执行器
 * @param fn 要执行的异步函数
 * @param config 重试配置
 * @returns Promise结果
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt < config.maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // 检查是否应该重试
      if (!config.retryCondition || !config.retryCondition(error)) {
        throw error;
      }
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === config.maxAttempts - 1) {
        break;
      }
      
      // 计算延迟时间并等待
      const delayMs = calculateBackoffDelay(attempt, config);
      console.log(`重试第 ${attempt + 1} 次，延迟 ${delayMs}ms`);
      await delay(delayMs);
    }
  }
  
  throw lastError;
}

/**
 * 带重试的fetch请求
 * @param url 请求URL
 * @param options fetch选项
 * @param retryConfig 重试配置
 * @returns Response对象
 */
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryConfig: Partial<RetryConfig> = {}
): Promise<Response> {
  const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
  
  return withRetry(async () => {
    const response = await fetch(url, options);
    
    // 检查响应状态
    if (!response.ok) {
      throw response;
    }
    
    return response;
  }, config);
}

/**
 * 创建重试装饰器
 * @param config 重试配置
 * @returns 装饰器函数
 */
export function createRetryDecorator(config: Partial<RetryConfig> = {}) {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };

  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;

    descriptor.value = async function (this: any, ...args: any[]) {
      return withRetry(() => method.apply(this, args), finalConfig);
    } as T;
  };
}

/**
 * 请求频率限制器
 */
export class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number, windowMs: number) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  /**
   * 检查是否可以发送请求
   * @returns 是否允许请求
   */
  canMakeRequest(): boolean {
    const now = Date.now();
    
    // 清理过期的请求记录
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    return this.requests.length < this.maxRequests;
  }

  /**
   * 记录一次请求
   */
  recordRequest(): void {
    this.requests.push(Date.now());
  }

  /**
   * 等待直到可以发送请求
   * @returns Promise<void>
   */
  async waitForSlot(): Promise<void> {
    while (!this.canMakeRequest()) {
      await delay(100); // 等待100ms后重新检查
    }
    this.recordRequest();
  }

  /**
   * 获取下次可用时间
   * @returns 毫秒数
   */
  getNextAvailableTime(): number {
    if (this.canMakeRequest()) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    return oldestRequest + this.windowMs - Date.now();
  }
}
