# 部署指南

## 概述

本项目使用 Cloudflare Pages（前端）和 Cloudflare Workers（后端API）进行部署。

## 前置要求

1. Cloudflare 账户
2. GitHub 账户
3. Node.js 18+ 
4. npm 或 yarn

## 环境配置

### 1. Cloudflare 配置

#### 创建 KV 命名空间

```bash
# 创建生产环境 KV 命名空间
npx wrangler kv:namespace create "STOCK_CACHE"
npx wrangler kv:namespace create "STOCK_CONFIG"

# 创建预览环境 KV 命名空间
npx wrangler kv:namespace create "STOCK_CACHE" --preview
npx wrangler kv:namespace create "STOCK_CONFIG" --preview
```

#### 更新 wrangler.toml

将创建的 KV 命名空间 ID 更新到 `workers/wrangler.toml` 文件中：

```toml
[[kv_namespaces]]
binding = "STOCK_CACHE"
id = "your-actual-cache-namespace-id"
preview_id = "your-actual-cache-preview-id"

[[kv_namespaces]]
binding = "STOCK_CONFIG"
id = "your-actual-config-namespace-id"
preview_id = "your-actual-config-preview-id"
```

### 2. GitHub Secrets 配置

在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加以下 secrets：

#### 必需的 Secrets

- `CLOUDFLARE_API_TOKEN`: Cloudflare API Token（需要 Zone:Read, Account:Read, Workers:Edit 权限）
- `CLOUDFLARE_ACCOUNT_ID`: Cloudflare 账户 ID
- `VITE_API_BASE_URL`: 前端 API 基础 URL（生产环境 Workers URL）
- `WORKERS_URL`: Workers 部署后的 URL（用于健康检查）
- `FRONTEND_URL`: 前端部署后的 URL（用于健康检查）

#### 获取 Cloudflare API Token

1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token"
3. 使用 "Custom token" 模板
4. 设置权限：
   - Account: Cloudflare Workers:Edit
   - Zone: Zone:Read
   - Account: Account:Read
5. 设置账户资源：Include - All accounts
6. 设置区域资源：Include - All zones

### 3. 环境变量说明

#### Workers 环境变量

| 变量名 | 说明 | 开发环境默认值 | 生产环境默认值 |
|--------|------|----------------|----------------|
| `ENVIRONMENT` | 环境标识 | development | production |
| `API_BASE_URL` | 东财API地址 | https://push2.eastmoney.com | https://push2.eastmoney.com |
| `CRON_ENABLED` | 是否启用定时任务 | true | true |
| `CACHE_TTL` | 缓存时间（秒） | 60 | 60 |
| `BATCH_SIZE` | 批处理大小 | 5 | 10 |
| `MAX_RETRIES` | 最大重试次数 | 3 | 3 |
| `LOG_LEVEL` | 日志级别 | debug | info |

#### 前端环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_API_BASE_URL` | API基础URL | http://localhost:8787 |
| `VITE_APP_TITLE` | 应用标题 | 股票资金流向监控 |
| `VITE_REFRESH_INTERVAL` | 刷新间隔（毫秒） | 60000 |
| `VITE_ENABLE_DEVTOOLS` | 是否启用开发工具 | false |

## 部署流程

### 1. 自动部署（推荐）

项目配置了 GitHub Actions 自动部署：

- **开发环境**: 推送到 `develop` 分支时自动部署
- **生产环境**: 推送到 `main` 分支时自动部署

### 2. 手动部署

#### 部署 Workers

```bash
cd workers

# 部署到开发环境
npm run deploy:dev

# 部署到生产环境
npm run deploy:prod
```

#### 部署前端

```bash
# 构建前端
npm run build

# 使用 Cloudflare Pages 部署
# 或者手动上传 dist 目录到 Cloudflare Pages
```

### 3. 本地开发

#### 启动 Workers 开发服务器

```bash
cd workers
npm run dev
```

#### 启动前端开发服务器

```bash
npm run dev
```

## 监控和维护

### 1. 健康检查

部署后可以通过以下端点检查服务状态：

- Workers API: `https://your-workers-url.workers.dev/health`
- API 测试: `https://your-workers-url.workers.dev/api/test`
- 定时任务状态: `https://your-workers-url.workers.dev/api/cron/status`

### 2. 日志查看

```bash
# 查看 Workers 日志
npx wrangler tail

# 查看特定环境的日志
npx wrangler tail --env production
```

### 3. KV 数据管理

```bash
# 列出 KV 键
npx wrangler kv:key list --binding STOCK_CACHE

# 获取 KV 值
npx wrangler kv:key get "stock_list" --binding STOCK_CONFIG

# 删除 KV 键
npx wrangler kv:key delete "key_name" --binding STOCK_CACHE
```

## 故障排除

### 常见问题

1. **部署失败**: 检查 API Token 权限和账户 ID
2. **KV 错误**: 确认 KV 命名空间 ID 正确
3. **CORS 错误**: 检查前端 API 基础 URL 配置
4. **定时任务不执行**: 确认 `CRON_ENABLED` 为 true

### 调试步骤

1. 检查 GitHub Actions 日志
2. 查看 Cloudflare Dashboard 中的 Workers 日志
3. 使用浏览器开发者工具检查网络请求
4. 检查环境变量配置

## 性能优化

### 1. 缓存策略

- 静态资源：1年缓存
- API 响应：根据数据类型设置不同缓存时间
- 页面：1小时缓存

### 2. CDN 优化

Cloudflare 自动提供全球 CDN 加速，无需额外配置。

### 3. 压缩优化

- 启用 Gzip/Brotli 压缩
- 图片优化
- 代码分割和懒加载

## 安全配置

### 1. CSP 策略

已在 `_headers` 文件中配置内容安全策略。

### 2. HTTPS 强制

Cloudflare 自动强制 HTTPS。

### 3. API 安全

- 实现请求频率限制
- 输入验证和清理
- 错误信息不暴露敏感信息
