/**
 * 批量请求测试工具
 * 用于测试和验证批量股票数据请求的修复效果
 */

import { stockDataApi } from '@/services/stockApi';

/**
 * 生成测试用的股票代码列表
 */
export function generateTestStockCodes(count: number): string[] {
  // 沪市股票代码 (6开头)
  const shStocks = [
    '600000', '600036', '600519', '600887', '600276', '600030', '600585', '600031',
    '600048', '600050', '600104', '600111', '600115', '600118', '600121', '600150',
    '600161', '600170', '600177', '600188', '600196', '600208', '600219', '600221',
    '600233', '600256', '600266', '600267', '600271', '600276', '600309', '600315'
  ];
  
  // 深市股票代码 (000开头)
  const szStocks = [
    '000001', '000002', '000063', '000069', '000100', '000157', '000166', '000333',
    '000338', '000425', '000538', '000568', '000625', '000651', '000661', '000725',
    '000768', '000776', '000783', '000858', '000876', '000895', '000938', '000959',
    '000977', '001979', '002001', '002007', '002024', '002027', '002032', '002044'
  ];
  
  const allStocks = [...shStocks, ...szStocks];
  
  // 随机选择指定数量的股票代码
  const shuffled = allStocks.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, allStocks.length));
}

/**
 * 测试批量请求功能
 */
export async function testBatchRequest(stockCount: number = 49) {
  console.log(`🧪 开始测试批量请求功能 - ${stockCount}个股票`);
  
  try {
    // 生成测试股票代码
    const testCodes = generateTestStockCodes(stockCount);
    console.log(`📋 测试股票代码:`, testCodes);
    
    // 执行批量请求
    const startTime = Date.now();
    const result = await stockDataApi.getBatchStockData(testCodes, 20, true);
    const duration = Date.now() - startTime;
    
    // 分析结果
    console.log(`✅ 测试完成，耗时: ${duration}ms`);
    console.log(`📊 结果统计:`, result.summary);
    console.log(`📈 成功率: ${((result.summary.success / result.summary.total) * 100).toFixed(1)}%`);
    
    // 检查是否有失败的股票
    if (result.summary.failed > 0) {
      console.warn(`⚠️  失败的股票:`, result.errors);
    }
    
    // 验证数据完整性
    const expectedTotal = testCodes.length;
    const actualTotal = Object.keys(result.results).length + Object.keys(result.errors).length;
    
    if (actualTotal !== expectedTotal) {
      console.error(`❌ 数据完整性检查失败: 期望${expectedTotal}个，实际${actualTotal}个`);
    } else {
      console.log(`✅ 数据完整性检查通过`);
    }
    
    return {
      success: true,
      duration,
      summary: result.summary,
      dataIntegrityCheck: actualTotal === expectedTotal
    };
    
  } catch (error) {
    console.error(`❌ 测试失败:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 压力测试 - 测试不同数量的股票请求
 */
export async function stressTest() {
  console.log(`🔥 开始压力测试`);
  
  const testCases = [10, 20, 30, 49, 60]; // 不同数量的股票
  const results: Array<{
    stockCount: number;
    success: boolean;
    duration?: number;
    successRate?: number;
    error?: string;
  }> = [];
  
  for (const stockCount of testCases) {
    console.log(`\n--- 测试 ${stockCount} 个股票 ---`);
    
    try {
      const result = await testBatchRequest(stockCount);
      results.push({
        stockCount,
        success: result.success,
        duration: result.duration,
        successRate: result.summary ? (result.summary.success / result.summary.total) * 100 : 0,
        error: result.error
      });
      
      // 测试间隔，避免API频率限制
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      results.push({
        stockCount,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
  
  // 输出压力测试结果
  console.log(`\n📊 压力测试结果汇总:`);
  console.table(results);
  
  return results;
}

/**
 * 在浏览器控制台中运行测试的便捷函数
 */
export function runQuickTest() {
  console.log(`🚀 快速测试 - 49个股票批量请求`);
  return testBatchRequest(49);
}

// 导出到全局对象，方便在浏览器控制台中使用
if (typeof window !== 'undefined') {
  (window as any).batchRequestTest = {
    test: testBatchRequest,
    stressTest,
    quickTest: runQuickTest,
    generateCodes: generateTestStockCodes
  };
}
