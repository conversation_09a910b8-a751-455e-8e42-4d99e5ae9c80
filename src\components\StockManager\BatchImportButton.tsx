import React, { useState, useRef } from 'react';
import { Upload, FileText, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface BatchImportResult {
  success: boolean;
  message: string;
  successCount: number;
  failedCount: number;
  duplicateCount: number;
  parseErrors: string[];
  addErrors: string[];
  totalRows: number;
}

interface BatchImportButtonProps {
  onImport: (csvContent: string) => Promise<BatchImportResult>;
  isLoading?: boolean;
  className?: string;
}

export function BatchImportButton({ onImport, isLoading = false, className = '' }: BatchImportButtonProps) {
  const [importResult, setImportResult] = useState<BatchImportResult | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('请选择CSV文件');
      return;
    }

    setIsImporting(true);
    setImportResult(null);

    try {
      // 读取文件内容
      const csvContent = await file.text();
      
      // 执行导入
      const result = await onImport(csvContent);
      setImportResult(result);
      
    } catch (error) {
      setImportResult({
        success: false,
        message: '文件读取失败',
        successCount: 0,
        failedCount: 0,
        duplicateCount: 0,
        parseErrors: [error instanceof Error ? error.message : '未知错误'],
        addErrors: [],
        totalRows: 0
      });
    } finally {
      setIsImporting(false);
      // 重置文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 打开文件选择对话框
  const handleButtonClick = () => {
    if (isLoading || isImporting) return;
    
    // 设置默认文件路径（如果支持的话）
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 关闭模态框
  const handleCloseModal = () => {
    setImportResult(null);
  };

  return (
    <>
      {/* 批量导入按钮 */}
      <button
        onClick={handleButtonClick}
        disabled={isLoading || isImporting}
        className={`
          flex items-center gap-2 px-3 py-1 text-sm 
          bg-green-50 text-green-600 hover:bg-green-100 
          disabled:bg-gray-50 disabled:text-gray-400
          rounded-lg transition-colors
          ${className}
        `}
        title="批量导入股票代码"
      >
        <Upload className="w-4 h-4" />
        {isImporting ? '导入中...' : '批量导入'}
      </button>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 导入结果模态框 */}
      {importResult && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-96 overflow-y-auto">
            {/* 标题 */}
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold">批量导入结果</h3>
            </div>

            {/* 结果摘要 */}
            <div className={`
              flex items-center gap-2 p-3 rounded-lg mb-4
              ${importResult.success 
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
              }
            `}>
              {importResult.success ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <XCircle className="w-4 h-4" />
              )}
              <span className="text-sm font-medium">{importResult.message}</span>
            </div>

            {/* 详细统计 */}
            <div className="space-y-2 mb-4">
              <div className="text-sm text-gray-600">
                <span className="font-medium">总行数：</span>{importResult.totalRows}
              </div>
              {importResult.successCount > 0 && (
                <div className="text-sm text-green-600">
                  <span className="font-medium">成功添加：</span>{importResult.successCount} 只股票
                </div>
              )}
              {importResult.duplicateCount > 0 && (
                <div className="text-sm text-yellow-600">
                  <span className="font-medium">跳过重复：</span>{importResult.duplicateCount} 只股票
                </div>
              )}
              {importResult.failedCount > 0 && (
                <div className="text-sm text-red-600">
                  <span className="font-medium">添加失败：</span>{importResult.failedCount} 只股票
                </div>
              )}
            </div>

            {/* 错误详情 */}
            {(importResult.parseErrors.length > 0 || importResult.addErrors.length > 0) && (
              <div className="mb-4">
                <div className="flex items-center gap-1 mb-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium text-gray-700">错误详情：</span>
                </div>
                <div className="max-h-32 overflow-y-auto bg-gray-50 rounded p-2">
                  {importResult.parseErrors.map((error, index) => (
                    <div key={`parse-${index}`} className="text-xs text-red-600 mb-1">
                      解析错误：{error}
                    </div>
                  ))}
                  {importResult.addErrors.map((error, index) => (
                    <div key={`add-${index}`} className="text-xs text-red-600 mb-1">
                      添加错误：{error}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 关闭按钮 */}
            <div className="flex justify-end">
              <button
                onClick={handleCloseModal}
                className="px-4 py-2 text-sm bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-lg transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default BatchImportButton;
