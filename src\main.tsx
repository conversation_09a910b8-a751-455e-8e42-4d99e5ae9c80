import React from 'react'
import ReactDOM from 'react-dom/client'
import { QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'
import { queryClient } from '@/utils/queryClient'
import App from './App.tsx'
import './index.css'

// 开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('./utils/__tests__/patternDetection.test').catch(() => {
    // 测试文件导入失败时静默处理
  });
  import('./components/DataDisplay/__tests__/MiniFlowChart.demo').catch(() => {
    // 演示组件导入失败时静默处理
  });
  import('./components/StockMonitor/__tests__/StockMonitorPanel.demo').catch(() => {
    // 监控面板演示组件导入失败时静默处理
  });
  import('./components/Layout/__tests__/Sidebar.integration.demo').catch(() => {
    // 侧边栏集成演示组件导入失败时静默处理
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
      {/* React Query开发工具 - 仅在开发环境显示 */}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  </React.StrictMode>,
)
