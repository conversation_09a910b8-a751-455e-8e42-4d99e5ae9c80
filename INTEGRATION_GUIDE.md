# 股票管理与实时监控功能集成指南

## 功能概述

本次更新将原本独立的实时监控功能完全集成到股票管理模块中，实现了统一的股票管理和监控界面。

## 主要变更

### 1. 功能合并
- ✅ 将实时监控功能直接嵌入到股票管理列表中
- ✅ 移除了侧边栏下方的独立实时监控面板
- ✅ 保留了两个模块的所有现有功能

### 2. 界面优化
- ✅ 侧边栏宽度从 320px 调整为 384px (w-96)，提供更多显示空间
- ✅ 股票列表项现在可以显示实时价格和资金流向数据
- ✅ 添加了实时监控开关，用户可以选择是否启用实时数据

### 3. 新增功能
- ✅ 实时监控开关：点击活动图标可开启/关闭实时监控
- ✅ V字形模式指示器：在股票列表中直接显示V字形模式
- ✅ 资金流向显示：显示最新资金流入和24小时变化
- ✅ 自动刷新：每60秒自动更新实时数据
- ✅ 手动刷新：点击活动图标可手动刷新数据

## 使用说明

### 启用实时监控
1. 在股票管理面板的右上角，点击活动图标 (Activity) 开启实时监控
2. 开启后，股票列表中每只股票都会显示：
   - 最新资金流入金额（绿色为流入，红色为流出）
   - 24小时变化（带箭头指示器）
   - V字形模式标识（黄色标签）

### 数据格式
- 资金流向数据自动格式化：
  - 超过1亿显示为"X.XX亿"
  - 超过1万显示为"X.XX万"
  - 其他显示原始数值

### 状态指示
- 蓝色活动图标：实时监控已启用
- 灰色活动图标：实时监控已关闭
- 旋转刷新图标：数据正在更新
- "实时监控"标签：在底部状态栏显示当前模式

## 技术实现

### 组件结构
```
StockManager (主组件)
├── StockInput (股票输入)
└── StockList (股票列表 - 集成实时监控)
    └── StockListItem (单个股票项 - 显示实时数据)
```

### 数据流
1. `StockManager` 管理实时监控开关状态
2. `StockList` 使用 `useBatchStockData` Hook 获取实时数据
3. `StockListItem` 显示格式化的实时数据和状态指示器

### 性能优化
- 只有在启用实时监控时才会发起数据请求
- 使用 React Query 进行数据缓存和自动刷新
- 使用 useMemo 优化数据处理性能

## 兼容性

- ✅ 保持所有原有的股票管理功能（添加、删除、选择）
- ✅ 保持所有原有的实时监控功能（数据更新、模式识别）
- ✅ 保持原有的API接口不变
- ✅ 保持原有的数据结构不变

## 后续优化建议

1. 可以考虑添加更多的排序选项（按资金流向、按变化幅度等）
2. 可以添加筛选功能（只显示V字形模式的股票等）
3. 可以添加更多的技术指标显示
4. 可以考虑添加声音提醒功能

## 测试建议

1. 测试实时监控开关的功能
2. 测试数据自动刷新功能
3. 测试手动刷新功能
4. 测试V字形模式识别
5. 测试在不同数据状态下的显示效果
6. 测试响应式布局在不同屏幕尺寸下的表现
