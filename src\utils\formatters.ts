/**
 * 数据格式化工具函数
 */

/**
 * 格式化金额数字
 * @param value 数值
 * @param unit 单位 ('yuan' | 'wan' | 'yi')
 * @param precision 小数位数
 * @returns 格式化后的字符串
 */
export function formatMoney(value: number, unit: 'yuan' | 'wan' | 'yi' = 'wan', precision: number = 2): string {
  if (value === 0) return '0';
  
  let divisor = 1;
  let unitText = '';
  
  switch (unit) {
    case 'yuan':
      divisor = 1;
      unitText = '元';
      break;
    case 'wan':
      divisor = 10000;
      unitText = '万';
      break;
    case 'yi':
      divisor = 100000000;
      unitText = '亿';
      break;
  }
  
  const result = value / divisor;
  return `${result.toFixed(precision)}${unitText}`;
}

/**
 * 智能格式化金额（自动选择合适的单位）
 * @param value 数值
 * @param precision 小数位数
 * @returns 格式化后的字符串
 */
export function formatMoneyAuto(value: number, precision: number = 2): string {
  const absValue = Math.abs(value);
  
  if (absValue >= 100000000) {
    return formatMoney(value, 'yi', precision);
  } else if (absValue >= 10000) {
    return formatMoney(value, 'wan', precision);
  } else {
    return formatMoney(value, 'yuan', 0);
  }
}

/**
 * 格式化百分比
 * @param value 数值
 * @param precision 小数位数
 * @returns 格式化后的字符串
 */
export function formatPercent(value: number, precision: number = 2): string {
  return `${value.toFixed(precision)}%`;
}

/**
 * 格式化时间
 * @param timeStr 时间字符串
 * @param format 格式类型
 * @returns 格式化后的时间字符串
 */
export function formatTime(timeStr: string, format: 'time' | 'date' | 'datetime' = 'time'): string {
  const date = new Date(timeStr);
  
  if (isNaN(date.getTime())) {
    return timeStr;
  }
  
  switch (format) {
    case 'time':
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    case 'date':
      return date.toLocaleDateString('zh-CN');
    case 'datetime':
      return date.toLocaleString('zh-CN');
    default:
      return timeStr;
  }
}

/**
 * 获取资金流向的颜色
 * @param value 资金流向值
 * @returns 颜色字符串
 */
export function getFlowColor(value: number): string {
  if (value > 0) {
    return '#ef4444'; // 红色 - 流入
  } else if (value < 0) {
    return '#22c55e'; // 绿色 - 流出
  } else {
    return '#6b7280'; // 灰色 - 无变化
  }
}

/**
 * 获取资金流向的方向文本
 * @param value 资金流向值
 * @returns 方向文本
 */
export function getFlowDirection(value: number): string {
  if (value > 0) {
    return '流入';
  } else if (value < 0) {
    return '流出';
  } else {
    return '平';
  }
}

/**
 * 格式化资金流向显示
 * @param value 资金流向值
 * @param showDirection 是否显示方向
 * @returns 格式化后的字符串
 */
export function formatFlow(value: number, showDirection: boolean = true): string {
  const absValue = Math.abs(value);
  const formattedValue = formatMoneyAuto(absValue);
  
  if (showDirection) {
    const direction = getFlowDirection(value);
    return `${direction} ${formattedValue}`;
  }
  
  return value >= 0 ? formattedValue : `-${formattedValue}`;
}

/**
 * 数字千分位格式化
 * @param value 数值
 * @param precision 小数位数
 * @returns 格式化后的字符串
 */
export function formatNumber(value: number, precision: number = 2): string {
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });
}

/**
 * 格式化股票代码显示
 * @param code 股票代码
 * @returns 格式化后的代码
 */
export function formatStockCode(code: string): string {
  // 确保是6位数字
  const cleanCode = code.replace(/\D/g, '');
  return cleanCode.padStart(6, '0');
}

/**
 * 获取市场标识
 * @param code 股票代码
 * @returns 市场标识
 */
export function getMarketLabel(code: string): string {
  const cleanCode = formatStockCode(code);
  
  if (cleanCode.startsWith('6')) {
    return 'SH'; // 上海
  } else if (cleanCode.startsWith('0') || cleanCode.startsWith('3')) {
    return 'SZ'; // 深圳
  } else {
    return '';
  }
}

/**
 * 格式化完整的股票代码显示
 * @param code 股票代码
 * @returns 格式化后的完整代码
 */
export function formatFullStockCode(code: string): string {
  const formattedCode = formatStockCode(code);
  const market = getMarketLabel(code);
  return market ? `${market}${formattedCode}` : formattedCode;
}

/**
 * 计算变化率
 * @param current 当前值
 * @param previous 之前值
 * @returns 变化率百分比
 */
export function calculateChangeRate(current: number, previous: number): number {
  if (previous === 0) return 0;
  return ((current - previous) / Math.abs(previous)) * 100;
}

/**
 * 格式化变化率显示
 * @param current 当前值
 * @param previous 之前值
 * @param precision 小数位数
 * @returns 格式化后的变化率字符串
 */
export function formatChangeRate(current: number, previous: number, precision: number = 2): string {
  const rate = calculateChangeRate(current, previous);
  const sign = rate > 0 ? '+' : '';
  return `${sign}${rate.toFixed(precision)}%`;
}

/**
 * 获取数据更新时间的相对显示
 * @param timestamp 时间戳字符串
 * @returns 相对时间字符串
 */
export function getRelativeTime(timestamp: string): string {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffMinutes < 1440) {
    const diffHours = Math.floor(diffMinutes / 60);
    return `${diffHours}小时前`;
  } else {
    const diffDays = Math.floor(diffMinutes / 1440);
    return `${diffDays}天前`;
  }
}

/**
 * 资金流向类型的中文名称映射
 */
export const FLOW_TYPE_NAMES = {
  mainNetInflow: '主力净流入',
  superLargeNetInflow: '超大单净流入',
  largeNetInflow: '大单净流入',
  mediumNetInflow: '中单净流入',
  smallNetInflow: '小单净流入',
} as const;

/**
 * 获取资金流向类型的中文名称
 * @param type 流向类型
 * @returns 中文名称
 */
export function getFlowTypeName(type: keyof typeof FLOW_TYPE_NAMES): string {
  return FLOW_TYPE_NAMES[type] || type;
}
