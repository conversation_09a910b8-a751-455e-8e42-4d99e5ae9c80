# GitHub 到 Cloudflare Pages 自动部署完整教程

## 📋 目录
1. [前置条件](#前置条件)
2. [GitHub 仓库配置](#github-仓库配置)
3. [Cloudflare Pages 设置步骤](#cloudflare-pages-设置步骤)
4. [自动化部署流程](#自动化部署流程)
5. [最佳实践](#最佳实践)
6. [常见问题排查](#常见问题排查)

---

## 🔧 前置条件

### 支持的项目类型
- ✅ 静态网站 (HTML/CSS/JS)
- ✅ React/Vue/Angular 等前端框架
- ✅ Vite/Webpack/Parcel 等构建工具项目
- ✅ Gatsby/Next.js 静态导出
- ✅ Jekyll/Hugo 等静态站点生成器

### 必需账户
- GitHub 账户（免费版即可）
- Cloudflare 账户（免费版即可）

### 本地开发环境
- Node.js 16+ （推荐 18+）
- Git
- 代码编辑器

---

## 📁 GitHub 仓库配置

### 1. 项目文件结构要求

```
your-project/
├── src/                    # 源代码目录
├── public/                 # 静态资源目录
├── dist/                   # 构建输出目录（自动生成）
├── package.json            # 项目配置文件 ⭐
├── package-lock.json       # 依赖锁定文件
├── vite.config.ts         # 构建配置文件 ⭐
├── _headers               # Cloudflare 头部配置 ⭐
├── _redirects             # Cloudflare 重定向配置 ⭐
└── README.md              # 项目说明
```

### 2. package.json 配置示例

确保您的 `package.json` 包含正确的构建脚本：

```json
{
  "name": "your-project",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "build:prod": "tsc && vite build --mode production",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.2.1",
    "typescript": "^5.2.2",
    "vite": "^5.0.8"
  }
}
```

### 3. 构建配置优化

**vite.config.ts 示例：**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',           // 输出目录
    minify: 'terser',         // 代码压缩
    sourcemap: false,         // 生产环境不生成源码映射
    rollupOptions: {
      output: {
        manualChunks: {       // 代码分割优化
          vendor: ['react', 'react-dom'],
        },
      },
    },
  },
})
```

### 4. Cloudflare 特定配置文件

**_headers 文件（根目录）：**
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Cache-Control: public, max-age=3600

/assets/*
  Cache-Control: public, max-age=31536000, immutable
```

**_redirects 文件（根目录）：**
```
# SPA 路由支持
/*    /index.html   200
```

### 5. 分支策略建议

```
main/master     ← 生产环境自动部署
├── develop     ← 开发环境预览部署
├── feature/*   ← 功能分支（可选预览）
└── hotfix/*    ← 紧急修复分支
```

---

## ☁️ Cloudflare Pages 设置步骤

### 步骤 1：登录 Cloudflare Dashboard

1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 登录您的账户
3. 在左侧导航栏选择 **"Pages"**

### 步骤 2：创建新项目

1. 点击 **"Create a project"** 按钮
2. 选择 **"Connect to Git"**
3. 选择 **"GitHub"** 作为 Git 提供商
4. 如果首次使用，需要授权 Cloudflare 访问您的 GitHub

### 步骤 3：选择仓库

1. 在仓库列表中找到您的项目
2. 点击 **"Begin setup"**
3. 如果看不到仓库，点击 **"Configure GitHub App"** 添加权限

### 步骤 4：配置构建设置

**基本设置：**
- **Project name**: `your-project-name`
- **Production branch**: `main` 或 `master`

**构建配置：**
```
Framework preset: Vite
Build command: npm run build
Build output directory: dist
Root directory: / (留空，除非项目在子目录)
```

**环境变量（如需要）：**
```
NODE_VERSION: 18
NPM_VERSION: 9
```

### 步骤 5：高级设置

**构建缓存：** 启用（提高构建速度）
**函数兼容性日期：** 使用最新日期
**兼容性标志：** 根据需要添加

### 步骤 6：部署

1. 点击 **"Save and Deploy"**
2. 等待首次构建完成（通常 2-5 分钟）
3. 构建成功后，您将获得一个 `.pages.dev` 域名

---

## 🚀 自动化部署流程

### Git Push 触发机制

```bash
# 推送到生产分支触发生产部署
git push origin main

# 推送到其他分支触发预览部署
git push origin develop
git push origin feature/new-feature
```

### 部署状态监控

1. **Cloudflare Dashboard**
   - Pages → 您的项目 → Deployments
   - 查看构建日志和状态

2. **GitHub 集成**
   - Pull Request 中会显示预览链接
   - Commit 状态检查显示部署结果

3. **通知设置**
   - Cloudflare Dashboard → Notifications
   - 配置邮件/Webhook 通知

### 部署日志查看

**构建日志位置：**
```
Cloudflare Dashboard → Pages → 项目名 → Deployments → 点击具体部署
```

**常见日志信息：**
```
✅ Cloning repository
✅ Installing dependencies
✅ Building application
✅ Deploying to Cloudflare's global network
🌐 Deployment successful: https://your-project.pages.dev
```

---

## 🎯 最佳实践

### 1. 环境变量管理

**在 Cloudflare Pages 中设置：**
```
Settings → Environment variables

Production:
- VITE_API_URL=https://api.yoursite.com
- VITE_APP_ENV=production

Preview:
- VITE_API_URL=https://api-staging.yoursite.com
- VITE_APP_ENV=staging
```

**在代码中使用：**
```typescript
const apiUrl = import.meta.env.VITE_API_URL
const appEnv = import.meta.env.VITE_APP_ENV
```

### 2. 预览部署设置

**自动预览：**
- 每个 Pull Request 自动创建预览部署
- 预览 URL 格式：`https://abc123.your-project.pages.dev`

**手动预览：**
```bash
# 使用 Wrangler CLI
npm install -g wrangler
wrangler pages publish dist --project-name your-project
```

### 3. 性能优化建议

**构建优化：**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['antd', '@mui/material'],
        },
      },
    },
  },
})
```

**缓存策略：**
```
# _headers
/assets/*
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=86400

/*.css
  Cache-Control: public, max-age=86400
```

### 4. 自定义域名配置

**步骤：**
1. Cloudflare Pages → 项目 → Custom domains
2. 点击 **"Set up a custom domain"**
3. 输入域名：`www.yoursite.com`
4. 按照提示配置 DNS 记录
5. 等待 SSL 证书自动配置

**DNS 配置示例：**
```
Type: CNAME
Name: www
Target: your-project.pages.dev
```

---

## 🔍 常见问题排查

### 构建失败

**问题 1：依赖安装失败**
```
Error: npm ERR! peer dep missing
```
**解决方案：**
```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

**问题 2：TypeScript 编译错误**
```
Error: TS2307: Cannot find module
```
**解决方案：**
```bash
# 检查 tsconfig.json 配置
# 确保所有依赖都已安装
npm install @types/node --save-dev
```

### 部署后访问问题

**问题：SPA 路由 404 错误**
**解决方案：** 确保 `_redirects` 文件配置正确
```
/*    /index.html   200
```

**问题：静态资源加载失败**
**解决方案：** 检查 `vite.config.ts` 中的 `base` 配置
```typescript
export default defineConfig({
  base: '/', // 确保为根路径
})
```

### 性能问题

**问题：首次加载慢**
**解决方案：**
1. 启用代码分割
2. 配置适当的缓存头
3. 压缩静态资源

**问题：构建时间长**
**解决方案：**
1. 启用构建缓存
2. 优化依赖结构
3. 使用更快的构建工具

---

## 🛠️ 高级配置示例

### 1. 多环境部署配置

**package.json 脚本增强：**
```json
{
  "scripts": {
    "build": "vite build",
    "build:staging": "vite build --mode staging",
    "build:production": "vite build --mode production",
    "deploy:preview": "npm run build && wrangler pages publish dist",
    "deploy:prod": "npm run build:production && wrangler pages publish dist --env production"
  }
}
```

**环境配置文件：**
```
.env.local          # 本地开发
.env.staging        # 预发布环境
.env.production     # 生产环境
```

### 2. GitHub Actions 集成（可选）

创建 `.github/workflows/deploy.yml`：
```yaml
name: Deploy to Cloudflare Pages

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: your-project-name
          directory: dist
```

### 3. 函数集成（Cloudflare Functions）

**创建 functions 目录：**
```
functions/
├── api/
│   ├── hello.ts
│   └── users/
│       └── [id].ts
└── _middleware.ts
```

**示例 API 函数：**
```typescript
// functions/api/hello.ts
export async function onRequest(context) {
  return new Response('Hello from Cloudflare Pages Functions!')
}
```

### 4. 安全配置增强

**_headers 完整配置：**
```
/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';

  # HSTS (if using custom domain)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

/assets/*
  Cache-Control: public, max-age=31536000, immutable

/api/*
  Cache-Control: no-cache, no-store, must-revalidate
```

---

## 🔧 故障排除详细指南

### 构建错误解决方案

**错误类型 1：内存不足**
```
Error: JavaScript heap out of memory
```
**解决方案：**
```json
// package.json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' vite build"
  }
}
```

**错误类型 2：模块解析失败**
```
Error: Failed to resolve import
```
**解决方案：**
```typescript
// vite.config.ts
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

**错误类型 3：环境变量未定义**
```
ReferenceError: process is not defined
```
**解决方案：**
```typescript
// vite.config.ts
export default defineConfig({
  define: {
    'process.env': process.env
  },
})
```

### 部署后问题解决

**问题 1：页面空白**
**检查清单：**
- [ ] 检查浏览器控制台错误
- [ ] 确认构建输出目录正确
- [ ] 验证 `_redirects` 文件配置
- [ ] 检查 CSP 头部设置

**问题 2：API 请求失败**
**解决方案：**
```typescript
// 使用相对路径或环境变量
const API_BASE = import.meta.env.VITE_API_URL || '/api'
```

**问题 3：字体/图片加载失败**
**解决方案：**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    assetsInlineLimit: 0, // 禁用内联，使用文件引用
  },
})
```

### 性能优化检查清单

**构建优化：**
- [ ] 启用代码分割
- [ ] 配置 Tree Shaking
- [ ] 压缩图片资源
- [ ] 移除未使用的依赖

**运行时优化：**
- [ ] 配置适当的缓存策略
- [ ] 启用 Gzip/Brotli 压缩
- [ ] 使用 CDN 加速
- [ ] 实施懒加载

---

## 📊 监控和分析

### 1. Cloudflare Analytics

**启用方法：**
1. Cloudflare Dashboard → Analytics & Logs
2. 选择您的 Pages 项目
3. 查看访问统计、性能指标

**关键指标：**
- 页面浏览量
- 独立访客数
- 响应时间
- 错误率

### 2. Web Vitals 监控

**集成 Web Vitals：**
```typescript
// src/utils/analytics.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // 发送到您的分析服务
  console.log(metric)
}

getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

### 3. 错误监控

**集成 Sentry（可选）：**
```typescript
// src/main.tsx
import * as Sentry from "@sentry/react"

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.VITE_APP_ENV,
})
```

---

## 📞 获取帮助

### 官方资源
- **Cloudflare Pages 文档：** https://developers.cloudflare.com/pages/
- **Cloudflare 社区论坛：** https://community.cloudflare.com/
- **Vite 官方文档：** https://vitejs.dev/

### 社区支持
- **Stack Overflow：** 标签 `cloudflare-pages`
- **Discord：** Cloudflare Developers
- **GitHub Discussions：** 相关项目讨论区

### 紧急支持
- **Cloudflare Support：** 付费用户可获得技术支持
- **状态页面：** https://www.cloudflarestatus.com/

---

## 📋 部署检查清单

### 部署前检查
- [ ] 代码已推送到 GitHub
- [ ] package.json 构建脚本正确
- [ ] 环境变量已配置
- [ ] _headers 和 _redirects 文件就位
- [ ] 本地构建测试通过

### 部署后验证
- [ ] 网站可正常访问
- [ ] 所有页面路由工作正常
- [ ] API 请求成功
- [ ] 静态资源加载正常
- [ ] 移动端适配良好
- [ ] SEO 元标签正确

### 性能检查
- [ ] Lighthouse 分数 > 90
- [ ] 首屏加载时间 < 3s
- [ ] 图片已优化
- [ ] 缓存策略生效

---

**🎉 恭喜！您已经掌握了 GitHub 到 Cloudflare Pages 的完整部署流程。**

这个教程涵盖了从基础配置到高级优化的所有内容。每次推送代码到 GitHub，Cloudflare Pages 都会自动构建和部署您的应用程序，为您提供快速、安全、全球分发的网站托管服务。
