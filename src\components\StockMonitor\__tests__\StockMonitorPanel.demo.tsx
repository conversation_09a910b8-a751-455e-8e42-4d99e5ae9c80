import React, { useState } from 'react';
import { StockMonitorItem } from '../StockMonitorItem';
import { Stock } from '@/types';
import { ProcessedStockData, KlineDataPoint } from '@/types/stock';

/**
 * 创建测试用的K线数据
 */
function createTestKlineData(values: number[]): KlineDataPoint[] {
  return values.map((value, index) => ({
    time: `2024-01-${String(index + 1).padStart(2, '0')} 09:30:00`,
    mainNetInflow: value,
    superLargeNetInflow: value * 0.4,
    largeNetInflow: value * 0.3,
    mediumNetInflow: value * 0.2,
    smallNetInflow: value * 0.1,
  }));
}

/**
 * 创建测试股票数据
 */
function createTestStockData(): { stocks: Stock[], results: Record<string, ProcessedStockData> } {
  const stocks: Stock[] = [
    { code: '000001', name: '平安银行' },
    { code: '000002', name: '万科A' },
    { code: '600036', name: '招商银行' },
    { code: '600519', name: '贵州茅台' },
    { code: '000858', name: '五粮液' },
    { code: '002415', name: '海康威视' },
  ];

  // 创建不同类型的数据
  const normalTrend = createTestKlineData([1000000, 1200000, 1100000, 1300000, 1250000, 1400000]);
  const vPatternTrend = createTestKlineData([2000000, 1500000, 1000000, 500000, 800000, 1200000, 1600000, 1800000]);
  const downTrend = createTestKlineData([2000000, 1800000, 1600000, 1400000, 1200000, 1000000]);
  const upTrend = createTestKlineData([1000000, 1200000, 1400000, 1600000, 1800000, 2000000]);
  const volatileTrend = createTestKlineData([1500000, 800000, 1800000, 600000, 2000000, 1000000]);
  const strongVPattern = createTestKlineData([3000000, 2000000, 1000000, 200000, 800000, 1500000, 2200000, 2800000]);

  const results: Record<string, ProcessedStockData> = {
    '000001': {
      summary: { code: '000001', name: '平安银行', market: 0, mainNetInflow: normalTrend[normalTrend.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: normalTrend,
      totalCount: normalTrend.length,
    },
    '000002': {
      summary: { code: '000002', name: '万科A', market: 0, mainNetInflow: vPatternTrend[vPatternTrend.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: vPatternTrend,
      totalCount: vPatternTrend.length,
    },
    '600036': {
      summary: { code: '600036', name: '招商银行', market: 1, mainNetInflow: downTrend[downTrend.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: downTrend,
      totalCount: downTrend.length,
    },
    '600519': {
      summary: { code: '600519', name: '贵州茅台', market: 1, mainNetInflow: upTrend[upTrend.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: upTrend,
      totalCount: upTrend.length,
    },
    '000858': {
      summary: { code: '000858', name: '五粮液', market: 0, mainNetInflow: volatileTrend[volatileTrend.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: volatileTrend,
      totalCount: volatileTrend.length,
    },
    '002415': {
      summary: { code: '002415', name: '海康威视', market: 0, mainNetInflow: strongVPattern[strongVPattern.length - 1].mainNetInflow, superLargeNetInflow: 0, largeNetInflow: 0, mediumNetInflow: 0, smallNetInflow: 0, lastUpdate: new Date().toISOString() },
      klines: strongVPattern,
      totalCount: strongVPattern.length,
    },
  };

  return { stocks, results };
}

/**
 * 股票监控面板演示组件
 */
export const StockMonitorPanelDemo: React.FC = () => {
  const [selectedStock, setSelectedStock] = useState<string | null>(null);
  const { stocks, results } = createTestStockData();

  const handleStockClick = (stockCode: string) => {
    setSelectedStock(stockCode);
    console.log('点击股票:', stockCode);
  };

  return (
    <div className="p-6 space-y-6 bg-gray-100 min-h-screen">
      <div className="bg-white rounded-lg p-6">
        <h1 className="text-2xl font-bold mb-6">股票监控面板演示</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 完整模式 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">完整模式</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="h-96 overflow-hidden">
                {/* 这里需要模拟 StockMonitorPanel 的功能 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">实时监控</h3>
                    <button className="text-xs text-gray-500">刷新</button>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-2 text-center">
                    <div className="bg-gray-50 rounded p-2">
                      <div className="text-xs text-gray-500">总计</div>
                      <div className="text-sm font-bold text-gray-900">{stocks.length}</div>
                    </div>
                    <div className="bg-red-50 rounded p-2">
                      <div className="text-xs text-red-600">V型</div>
                      <div className="text-sm font-bold text-red-600">2</div>
                    </div>
                    <div className="bg-green-50 rounded p-2">
                      <div className="text-xs text-green-600">流入</div>
                      <div className="text-sm font-bold text-green-600">4</div>
                    </div>
                    <div className="bg-blue-50 rounded p-2">
                      <div className="text-xs text-blue-600">成功</div>
                      <div className="text-sm font-bold text-blue-600">{stocks.length}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {stocks.map(stock => (
                      <StockMonitorItem
                        key={stock.code}
                        stock={stock}
                        data={results[stock.code]}
                        hasVPattern={stock.code === '000002' || stock.code === '002415'}
                        onClick={handleStockClick}
                        compact={false}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 紧凑模式 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">紧凑模式</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="h-96 overflow-hidden">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">实时监控</h3>
                    <button className="text-xs text-gray-500">刷新</button>
                  </div>
                  
                  <div className="space-y-1">
                    {stocks.map(stock => (
                      <StockMonitorItem
                        key={stock.code}
                        stock={stock}
                        data={results[stock.code]}
                        hasVPattern={stock.code === '000002' || stock.code === '002415'}
                        onClick={handleStockClick}
                        compact={true}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 单个组件测试 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">单个监控项测试</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {stocks.map(stock => (
              <div key={stock.code} className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">
                  {stock.name} ({stock.code})
                  {(stock.code === '000002' || stock.code === '002415') && (
                    <span className="ml-2 text-xs text-red-600">V字型</span>
                  )}
                </h4>
                <StockMonitorItem
                  stock={stock}
                  data={results[stock.code]}
                  hasVPattern={stock.code === '000002' || stock.code === '002415'}
                  onClick={handleStockClick}
                  compact={false}
                />
              </div>
            ))}
          </div>
        </div>

        {/* 状态测试 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">状态测试</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 无数据状态 */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">无数据状态</h4>
              <StockMonitorItem
                stock={{ code: '999999', name: '测试股票' }}
                data={null}
                hasVPattern={false}
                onClick={handleStockClick}
              />
            </div>

            {/* 空数据状态 */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">空K线数据</h4>
              <StockMonitorItem
                stock={{ code: '888888', name: '空数据股票' }}
                data={{ summary: null, klines: [], totalCount: 0 }}
                hasVPattern={false}
                onClick={handleStockClick}
              />
            </div>

            {/* V字型高亮 */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">V字型高亮</h4>
              <StockMonitorItem
                stock={stocks[1]}
                data={results[stocks[1].code]}
                hasVPattern={true}
                onClick={handleStockClick}
              />
            </div>
          </div>
        </div>

        {/* 选中状态显示 */}
        {selectedStock && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">选中股票</h4>
            <p className="text-sm text-blue-700">
              股票代码: {selectedStock} - {stocks.find(s => s.code === selectedStock)?.name}
            </p>
            <button
              onClick={() => setSelectedStock(null)}
              className="mt-2 text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
            >
              清除选择
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// 如果在浏览器环境中，将演示组件挂载到window对象
if (typeof window !== 'undefined') {
  (window as any).StockMonitorPanelDemo = StockMonitorPanelDemo;
  console.log('📊 股票监控面板演示已加载，在控制台运行 StockMonitorPanelDemo 查看组件');
}

export default StockMonitorPanelDemo;
