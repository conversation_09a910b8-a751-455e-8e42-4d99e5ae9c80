// 调试我们的API服务逻辑

// 从validator.ts复制的函数
function validateStockCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }
  return /^\d{6}$/.test(code.trim());
}

function sanitizeStockCode(code) {
  if (!code || typeof code !== 'string') {
    return '';
  }
  return code.trim().replace(/[^\d]/g, '');
}

function validateEastmoneyResponse(data) {
  if (!data || typeof data !== 'object') {
    console.log('验证失败: 数据不是对象');
    return false;
  }
  
  if (typeof data.rc !== 'number' || typeof data.rt !== 'number') {
    console.log('验证失败: rc或rt不是数字', { rc: data.rc, rt: data.rt });
    return false;
  }
  
  if (!data.data || typeof data.data !== 'object') {
    console.log('验证失败: data字段无效');
    return false;
  }
  
  const { data: responseData } = data;
  
  if (!responseData.code || !responseData.name || typeof responseData.market !== 'number') {
    console.log('验证失败: 缺少必要字段', {
      code: responseData.code,
      name: responseData.name,
      market: responseData.market,
      marketType: typeof responseData.market
    });
    return false;
  }
  
  if (!Array.isArray(responseData.klines)) {
    console.log('验证失败: klines不是数组');
    return false;
  }
  
  return true;
}

function validateKlineData(kline) {
  if (!kline || typeof kline !== 'string') {
    return false;
  }
  
  const parts = kline.split(',');
  if (parts.length < 6) {
    return false;
  }
  
  // 检查时间格式
  const timePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
  if (!timePattern.test(parts[0])) {
    return false;
  }
  
  // 检查数值部分
  for (let i = 1; i < 6; i++) {
    if (isNaN(parseFloat(parts[i]))) {
      return false;
    }
  }
  
  return true;
}

function safeJsonParse(jsonString) {
  try {
    const data = JSON.parse(jsonString);
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '未知解析错误' 
    };
  }
}

// 模拟RateLimiter
class RateLimiter {
  constructor(maxRequests, windowMs) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }
  
  async waitForSlot() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      if (waitTime > 0) {
        console.log(`频率限制: 等待 ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    this.requests.push(now);
  }
}

// 模拟EastmoneyApiService
class EastmoneyApiService {
  constructor() {
    this.baseUrl = 'https://push2.eastmoney.com';
    this.rateLimiter = new RateLimiter(60, 60000);
  }
  
  async getStockFlowData(stockCode, limit = 240) {
    try {
      console.log(`\n=== 获取股票数据: ${stockCode} ===`);
      
      // 验证和清洗股票代码
      const cleanCode = sanitizeStockCode(stockCode);
      console.log('清洗后的代码:', cleanCode);
      
      if (!validateStockCode(cleanCode)) {
        console.log('股票代码验证失败');
        return {
          success: false,
          message: '无效的股票代码',
          timestamp: new Date().toISOString(),
        };
      }

      // 等待频率限制
      await this.rateLimiter.waitForSlot();

      // 构建请求URL
      const url = this.buildFlowDataUrl(cleanCode, limit);
      console.log('请求URL:', url);
      
      // 生成请求头
      const headers = this.generateHeaders(cleanCode);

      // 发送请求
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });
      
      console.log('响应状态:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 解析响应
      const responseText = await response.text();
      console.log('响应文本长度:', responseText.length);
      
      const parseResult = safeJsonParse(responseText);
      
      if (!parseResult.success) {
        console.log('JSON解析失败:', parseResult.error);
        return {
          success: false,
          message: `数据解析失败: ${parseResult.error}`,
          timestamp: new Date().toISOString(),
        };
      }

      const apiData = parseResult.data;
      console.log('解析后的数据结构:', {
        rc: apiData.rc,
        rt: apiData.rt,
        hasData: !!apiData.data,
        dataCode: apiData.data?.code,
        dataName: apiData.data?.name,
        dataMarket: apiData.data?.market,
        klinesLength: apiData.data?.klines?.length,
      });

      // 验证响应数据
      if (!validateEastmoneyResponse(apiData)) {
        console.log('东方财富响应验证失败');
        return {
          success: false,
          message: '响应数据格式无效',
          timestamp: new Date().toISOString(),
        };
      }

      // 检查API返回码
      if (apiData.rc !== 0) {
        console.log('API返回错误码:', apiData.rc);
        return {
          success: false,
          message: `API返回错误码: ${apiData.rc}`,
          timestamp: new Date().toISOString(),
        };
      }

      // 处理和验证数据
      const processedData = this.processFlowData(apiData);
      console.log('数据处理完成，K线数量:', processedData.klines?.length);
      
      return {
        success: true,
        data: processedData,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      console.error('获取股票资金流向数据失败:', error);
      
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString(),
      };
    }
  }
  
  buildFlowDataUrl(stockCode, limit) {
    const marketCode = stockCode.startsWith('6') ? 1 : 0;
    const secid = `${marketCode}.${stockCode}`;
    
    const params = new URLSearchParams({
      secid,
      klt: '1',
      lmt: limit.toString(),
      fields1: 'f1,f2,f3,f7',
      fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
    });

    return `${this.baseUrl}/api/qt/stock/fflow/kline/get?${params.toString()}`;
  }
  
  generateHeaders(stockCode) {
    return {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': '*/*',
      'Referer': `https://data.eastmoney.com/zjlx/${stockCode}.html`,
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    };
  }
  
  processFlowData(apiData) {
    const { data } = apiData;
    const processedKlines = [];

    // 处理K线数据
    for (const kline of data.klines) {
      if (!validateKlineData(kline)) {
        console.log('跳过无效K线数据:', kline);
        continue;
      }

      const parts = kline.split(',');
      const [time, mainNet, superLargeNet, largeNet, mediumNet, smallNet] = parts;

      processedKlines.push({
        time,
        mainNetInflow: parseFloat(mainNet),
        superLargeNetInflow: parseFloat(superLargeNet),
        largeNetInflow: parseFloat(largeNet),
        mediumNetInflow: parseFloat(mediumNet),
        smallNetInflow: parseFloat(smallNet),
      });
    }

    // 计算汇总数据
    const summary = {
      code: data.code,
      name: data.name,
      market: data.market,
      lastUpdate: new Date().toISOString().slice(0, 16).replace('T', ' '),
      mainNetInflow: processedKlines.reduce((sum, k) => sum + k.mainNetInflow, 0),
      superLargeNetInflow: processedKlines.reduce((sum, k) => sum + k.superLargeNetInflow, 0),
      largeNetInflow: processedKlines.reduce((sum, k) => sum + k.largeNetInflow, 0),
      mediumNetInflow: processedKlines.reduce((sum, k) => sum + k.mediumNetInflow, 0),
      smallNetInflow: processedKlines.reduce((sum, k) => sum + k.smallNetInflow, 0),
    };

    return {
      summary,
      klines: processedKlines,
      totalCount: processedKlines.length,
    };
  }
}

// 测试函数
async function testService() {
  const apiService = new EastmoneyApiService();
  
  console.log('=== 测试单个股票 ===');
  const result1 = await apiService.getStockFlowData('600121', 20);
  console.log('结果1:', result1.success ? '成功' : '失败', result1.message);
  
  console.log('\n=== 测试批量股票 ===');
  const codes = ['600121', '600793', '603067'];
  
  for (const code of codes) {
    const result = await apiService.getStockFlowData(code, 20);
    console.log(`${code}:`, result.success ? '成功' : '失败', result.message);
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

testService();
