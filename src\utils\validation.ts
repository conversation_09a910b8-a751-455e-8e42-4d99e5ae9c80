import { ValidationResult } from '@/types';

/**
 * 验证股票代码格式
 * @param code 股票代码
 * @returns 验证结果
 */
export function validateStockCode(code: string): ValidationResult {
  // 去除空格
  const trimmedCode = code.trim();
  
  // 检查是否为空
  if (!trimmedCode) {
    return {
      isValid: false,
      message: '股票代码不能为空',
    };
  }
  
  // 检查长度是否为6位
  if (trimmedCode.length !== 6) {
    return {
      isValid: false,
      message: '股票代码必须为6位数字',
    };
  }
  
  // 检查是否全为数字
  if (!/^\d{6}$/.test(trimmedCode)) {
    return {
      isValid: false,
      message: '股票代码只能包含数字',
    };
  }
  
  // 检查是否为有效的股票代码范围
  const codeNum = parseInt(trimmedCode);
  
  // 沪市主板: 600000-699999
  // 深市主板: 000000-099999
  // 深市中小板: 002000-004999
  // 创业板: 300000-399999
  // 科创板: 688000-689999
  // 北交所: 430000-899999
  const isValidRange = 
    (codeNum >= 600000 && codeNum <= 699999) || // 沪市主板
    (codeNum >= 0 && codeNum <= 99999) ||       // 深市主板
    (codeNum >= 2000 && codeNum <= 4999) ||     // 深市中小板
    (codeNum >= 300000 && codeNum <= 399999) || // 创业板
    (codeNum >= 688000 && codeNum <= 689999) || // 科创板
    (codeNum >= 430000 && codeNum <= 899999);   // 北交所
  
  if (!isValidRange) {
    return {
      isValid: false,
      message: '请输入有效的股票代码',
    };
  }
  
  return {
    isValid: true,
  };
}

/**
 * 格式化股票代码（补零）
 * @param code 股票代码
 * @returns 格式化后的代码
 */
export function formatStockCode(code: string): string {
  const trimmedCode = code.trim();
  return trimmedCode.padStart(6, '0');
}

/**
 * 检查股票代码是否重复
 * @param code 要检查的代码
 * @param existingCodes 已存在的代码列表
 * @returns 是否重复
 */
export function isDuplicateCode(code: string, existingCodes: string[]): boolean {
  const formattedCode = formatStockCode(code);
  return existingCodes.includes(formattedCode);
}

/**
 * 批量验证股票代码
 * @param codes 股票代码数组
 * @returns 验证结果数组
 */
export function validateStockCodes(codes: string[]): ValidationResult[] {
  return codes.map(code => validateStockCode(code));
}

/**
 * 从secid中提取股票代码
 * @param secid 完整的secid（如：000001.SZ, 600036.SH）
 * @returns 6位股票代码
 */
export function extractStockCodeFromSecid(secid: string): string {
  if (!secid || typeof secid !== 'string') {
    return '';
  }

  // 提取点号前的部分，并去除非数字字符
  const codePart = secid.split('.')[0];
  const cleanCode = codePart.replace(/\D/g, '');

  // 确保是6位数字
  return cleanCode.padStart(6, '0').slice(0, 6);
}

/**
 * 股票信息接口
 */
export interface StockInfo {
  code: string;
  name: string;
}

/**
 * 解析CSV文件内容
 * @param csvContent CSV文件内容字符串
 * @returns 解析结果
 */
export function parseCSVContent(csvContent: string): {
  success: boolean;
  stockInfos: StockInfo[];
  errors: string[];
  totalRows: number;
} {
  const errors: string[] = [];
  const stockInfos: StockInfo[] = [];

  try {
    const lines = csvContent.trim().split('\n');

    if (lines.length === 0) {
      return {
        success: false,
        stockInfos: [],
        errors: ['CSV文件为空'],
        totalRows: 0
      };
    }

    // 解析表头，查找secID和secShortName列
    const headerLine = lines[0];
    const headers = headerLine.split(',').map(h => h.trim());

    const secidIndex = headers.findIndex(h =>
      h.toLowerCase() === 'secid' ||
      h.toLowerCase() === 'secID' ||
      h === 'secID'
    );

    const nameIndex = headers.findIndex(h =>
      h.toLowerCase() === 'secshortname' ||
      h.toLowerCase() === 'secShortName' ||
      h === 'secShortName' ||
      h.toLowerCase() === 'shortname' ||
      h.toLowerCase() === 'name' ||
      h === 'name'
    );

    if (secidIndex === -1) {
      return {
        success: false,
        stockInfos: [],
        errors: ['未找到secID列，请确保CSV文件包含secID列'],
        totalRows: lines.length - 1
      };
    }

    // 解析数据行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue; // 跳过空行

      const columns = line.split(',').map(c => c.trim());

      if (columns.length <= secidIndex) {
        errors.push(`第${i + 1}行：列数不足`);
        continue;
      }

      const secid = columns[secidIndex];
      if (!secid) {
        errors.push(`第${i + 1}行：secID为空`);
        continue;
      }

      const stockCode = extractStockCodeFromSecid(secid);
      if (!stockCode) {
        errors.push(`第${i + 1}行：无法从secID "${secid}" 提取股票代码`);
        continue;
      }

      // 验证股票代码
      const validation = validateStockCode(stockCode);
      if (!validation.isValid) {
        errors.push(`第${i + 1}行：股票代码 "${stockCode}" 无效 - ${validation.message}`);
        continue;
      }

      // 获取股票名称
      let stockName = `股票${stockCode}`; // 默认名称
      if (nameIndex !== -1 && columns.length > nameIndex && columns[nameIndex]) {
        stockName = columns[nameIndex].trim();
      }

      // 避免重复（基于股票代码）
      const existingStock = stockInfos.find(info => info.code === stockCode);
      if (!existingStock) {
        stockInfos.push({
          code: stockCode,
          name: stockName
        });
      }
    }

    return {
      success: stockInfos.length > 0,
      stockInfos,
      errors,
      totalRows: lines.length - 1
    };

  } catch (error) {
    return {
      success: false,
      stockInfos: [],
      errors: [`解析CSV文件失败: ${error instanceof Error ? error.message : '未知错误'}`],
      totalRows: 0
    };
  }
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => func(...args), delay);
  };
}
