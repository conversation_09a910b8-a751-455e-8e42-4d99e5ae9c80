# CORS问题解决 - 完整部署指南

## 🚨 问题诊断

您遇到的CORS错误是因为：
1. ✅ 前端已部署到 `sto-fund.pages.dev`
2. ❌ **后端API（Cloudflare Workers）尚未部署**
3. ❌ 前端配置的API URL包含占位符，不是真实域名

## 🛠️ 解决方案

### 步骤1：配置Cloudflare KV命名空间

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 选择您的账户 → Workers & Pages → KV
3. 创建以下KV命名空间：

```bash
# 生产环境
名称: gupiao-stock-cache-prod
名称: gupiao-stock-config-prod

# 预览环境  
名称: gupiao-stock-cache-preview
名称: gupiao-stock-config-preview
```

4. 记录创建后的命名空间ID，格式类似：`a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`

### 步骤2：更新wrangler.toml配置

编辑 `workers/wrangler.toml`，将占位符替换为实际的KV命名空间ID：

```toml
[[kv_namespaces]]
binding = "STOCK_CACHE"
id = "您的实际STOCK_CACHE命名空间ID"
preview_id = "您的实际STOCK_CACHE预览命名空间ID"

[[kv_namespaces]]
binding = "STOCK_CONFIG"
id = "您的实际STOCK_CONFIG命名空间ID"
preview_id = "您的实际STOCK_CONFIG预览命名空间ID"
```

### 步骤3：配置GitHub Secrets

在GitHub仓库设置中添加以下Secrets：

1. 访问 `https://github.com/YOUR_USERNAME/gupiao_zijinliu/settings/secrets/actions`
2. 添加以下Secrets：

```
CLOUDFLARE_API_TOKEN=您的Cloudflare API Token
CLOUDFLARE_ACCOUNT_ID=您的Cloudflare账户ID
VITE_API_BASE_URL=https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev
WORKERS_URL=https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev
FRONTEND_URL=https://sto-fund.pages.dev
```

**获取Cloudflare API Token**：
1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token" → "Custom token"
3. 设置权限：
   - Account: Cloudflare Workers:Edit
   - Zone: Zone:Read
   - Account: Account:Read
4. 账户资源：Include - All accounts
5. 区域资源：Include - All zones

**获取Account ID**：
在Cloudflare Dashboard右侧边栏可以找到Account ID

### 步骤4：更新前端API URL

编辑 `.env.production`，将 `YOUR_ACCOUNT` 替换为您的实际Cloudflare账户子域名：

```env
VITE_API_BASE_URL=https://gupiao-zijinliu-api-prod.您的账户名.workers.dev
```

### 步骤5：触发部署

1. 提交并推送代码到main分支：
```bash
git add .
git commit -m "fix: 配置Workers部署和CORS设置"
git push origin main
```

2. GitHub Actions将自动：
   - 构建并部署前端到Cloudflare Pages
   - 构建并部署Workers API到Cloudflare Workers
   - 执行健康检查

### 步骤6：验证部署

部署完成后：

1. **检查Workers部署**：
   访问 `https://gupiao-zijinliu-api-prod.您的账户名.workers.dev/health`
   应该返回JSON状态信息

2. **检查API文档**：
   访问 `https://gupiao-zijinliu-api-prod.您的账户名.workers.dev/api/docs`

3. **测试CORS**：
   在浏览器开发者工具中，从 `sto-fund.pages.dev` 发起API请求应该成功

## 🔧 故障排除

### 如果仍有CORS错误：

1. **检查Workers环境变量**：
   确保 `ENVIRONMENT=production` 已正确设置

2. **检查CORS配置**：
   Workers代码中的CORS配置应该自动允许 `sto-fund.pages.dev`

3. **手动测试API**：
```bash
curl -H "Origin: https://sto-fund.pages.dev" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://gupiao-zijinliu-api-prod.您的账户名.workers.dev/api/stocks
```

应该返回包含 `Access-Control-Allow-Origin` 头的响应

### 如果部署失败：

1. **检查GitHub Actions日志**：
   在仓库的Actions标签页查看详细错误信息

2. **验证Secrets配置**：
   确保所有必需的GitHub Secrets都已正确设置

3. **检查KV命名空间**：
   确保KV命名空间ID正确且存在

## 📞 需要帮助？

如果按照以上步骤操作后仍有问题，请提供：
1. GitHub Actions的错误日志
2. 浏览器控制台的完整错误信息
3. Workers部署后的实际URL

我将帮您进一步诊断和解决问题。
