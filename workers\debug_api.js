// 调试东方财富API响应格式的脚本

// 验证函数（从validator.ts复制）
function validateEastmoneyResponse(data) {
  if (!data || typeof data !== 'object') {
    console.log('验证失败: 数据不是对象');
    return false;
  }

  // 检查基本字段
  if (typeof data.rc !== 'number' || typeof data.rt !== 'number') {
    console.log('验证失败: rc或rt不是数字', { rc: data.rc, rt: data.rt });
    return false;
  }

  // 检查数据字段
  if (!data.data || typeof data.data !== 'object') {
    console.log('验证失败: data字段无效');
    return false;
  }

  const { data: responseData } = data;

  // 检查必要字段
  if (!responseData.code || !responseData.name || typeof responseData.market !== 'number') {
    console.log('验证失败: 缺少必要字段', {
      code: responseData.code,
      name: responseData.name,
      market: responseData.market,
      marketType: typeof responseData.market
    });
    return false;
  }

  // 检查klines数据
  if (!Array.isArray(responseData.klines)) {
    console.log('验证失败: klines不是数组');
    return false;
  }

  console.log('验证成功');
  return true;
}

async function testSingleStock() {
  console.log('\n=== 测试单个股票API ===');
  const stockCode = '600121';
  const limit = 20;

  // 构建URL
  const marketCode = stockCode.startsWith('6') ? 1 : 0;
  const secid = `${marketCode}.${stockCode}`;

  const params = new URLSearchParams({
    secid,
    klt: '1', // 1分钟K线
    lmt: limit.toString(),
    fields1: 'f1,f2,f3,f7',
    fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
  });

  const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

  console.log('请求URL:', url);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Referer': `https://data.eastmoney.com/zjlx/${stockCode}.html`,
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
    });

    console.log('响应状态:', response.status, response.statusText);

    const responseText = await response.text();
    console.log('响应文本长度:', responseText.length);

    try {
      const jsonData = JSON.parse(responseText);
      console.log('JSON解析成功');
      console.log('响应结构:', {
        rc: jsonData.rc,
        rt: jsonData.rt,
        hasData: !!jsonData.data,
        dataKeys: jsonData.data ? Object.keys(jsonData.data) : [],
        dataCode: jsonData.data?.code,
        dataName: jsonData.data?.name,
        dataMarket: jsonData.data?.market,
        klinesLength: jsonData.data?.klines?.length,
      });

      // 运行验证
      const isValid = validateEastmoneyResponse(jsonData);
      console.log('验证结果:', isValid);

    } catch (parseError) {
      console.error('JSON解析失败:', parseError.message);
    }

  } catch (error) {
    console.error('请求失败:', error.message);
  }
}

async function testBatchStocks() {
  console.log('\n=== 测试批量股票处理 ===');
  const stockCodes = ['600121', '600793', '603067'];

  for (const code of stockCodes) {
    console.log(`\n--- 测试股票 ${code} ---`);
    await testSingleStockInBatch(code);
    // 添加延迟避免频率限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

async function testSingleStockInBatch(stockCode) {
  const limit = 20;

  // 构建URL
  const marketCode = stockCode.startsWith('6') ? 1 : 0;
  const secid = `${marketCode}.${stockCode}`;

  const params = new URLSearchParams({
    secid,
    klt: '1',
    lmt: limit.toString(),
    fields1: 'f1,f2,f3,f7',
    fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
  });

  const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Referer': `https://data.eastmoney.com/zjlx/${stockCode}.html`,
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
    });

    console.log(`${stockCode} 响应状态:`, response.status);

    if (!response.ok) {
      console.error(`${stockCode} HTTP错误:`, response.status, response.statusText);
      return;
    }

    const responseText = await response.text();

    try {
      const jsonData = JSON.parse(responseText);
      const isValid = validateEastmoneyResponse(jsonData);
      console.log(`${stockCode} 验证结果:`, isValid);

      if (!isValid) {
        console.log(`${stockCode} 详细数据:`, JSON.stringify(jsonData, null, 2));
      }

    } catch (parseError) {
      console.error(`${stockCode} JSON解析失败:`, parseError.message);
      console.log(`${stockCode} 响应文本:`, responseText.substring(0, 200));
    }

  } catch (error) {
    console.error(`${stockCode} 请求失败:`, error.message);
  }
}

// 运行测试
async function runAllTests() {
  await testSingleStock();
  await testBatchStocks();
}

runAllTests();
