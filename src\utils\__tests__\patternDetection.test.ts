import { detectVPattern, isVPattern } from '../patternDetection';
import { KlineDataPoint } from '@/types/stock';

/**
 * 创建测试用的K线数据
 */
function createTestKlineData(values: number[]): KlineDataPoint[] {
  return values.map((value, index) => ({
    time: `2024-01-${String(index + 1).padStart(2, '0')} 09:30:00`,
    mainNetInflow: value,
    superLargeNetInflow: value * 0.4,
    largeNetInflow: value * 0.3,
    mediumNetInflow: value * 0.2,
    smallNetInflow: value * 0.1,
  }));
}

/**
 * 测试V字型模式识别算法
 */
function testVPatternDetection() {
  console.log('🧪 开始测试V字型模式识别算法...\n');

  // 测试1：典型的V字型模式
  console.log('📊 测试1：典型的V字型模式');
  const vPatternData = createTestKlineData([
    2000000,  // 起始点
    1500000,  // 下跌
    1000000,  // 继续下跌
    500000,   // 最低点
    800000,   // 开始反弹
    1200000,  // 继续反弹
    1600000,  // 强劲反弹
  ]);
  
  const vResult = detectVPattern(vPatternData);
  console.log('结果:', {
    hasVPattern: vResult.hasVPattern,
    confidence: vResult.confidence.toFixed(2),
    dropAmount: vResult.dropAmount,
    reboundAmount: vResult.reboundAmount,
    dropRatio: (vResult.dropRatio * 100).toFixed(1) + '%',
    reboundRatio: (vResult.reboundRatio * 100).toFixed(1) + '%',
  });
  console.log('✅ 预期：应该检测到V字型模式\n');

  // 测试2：持续下跌（非V字型）
  console.log('📊 测试2：持续下跌模式');
  const downTrendData = createTestKlineData([
    2000000,
    1800000,
    1600000,
    1400000,
    1200000,
    1000000,
    800000,
  ]);
  
  const downResult = detectVPattern(downTrendData);
  console.log('结果:', {
    hasVPattern: downResult.hasVPattern,
    confidence: downResult.confidence.toFixed(2),
  });
  console.log('✅ 预期：不应该检测到V字型模式\n');

  // 测试3：持续上涨（非V字型）
  console.log('📊 测试3：持续上涨模式');
  const upTrendData = createTestKlineData([
    1000000,
    1200000,
    1400000,
    1600000,
    1800000,
    2000000,
    2200000,
  ]);
  
  const upResult = detectVPattern(upTrendData);
  console.log('结果:', {
    hasVPattern: upResult.hasVPattern,
    confidence: upResult.confidence.toFixed(2),
  });
  console.log('✅ 预期：不应该检测到V字型模式\n');

  // 测试4：数据点不足
  console.log('📊 测试4：数据点不足');
  const insufficientData = createTestKlineData([1000000, 500000, 800000]);
  
  const insufficientResult = detectVPattern(insufficientData);
  console.log('结果:', {
    hasVPattern: insufficientResult.hasVPattern,
    confidence: insufficientResult.confidence.toFixed(2),
  });
  console.log('✅ 预期：不应该检测到V字型模式（数据不足）\n');

  // 测试5：弱V字型模式（幅度较小）
  console.log('📊 测试5：弱V字型模式');
  const weakVData = createTestKlineData([
    1000000,
    950000,
    900000,
    850000,
    900000,
    950000,
    1000000,
  ]);
  
  const weakResult = detectVPattern(weakVData);
  console.log('结果:', {
    hasVPattern: weakResult.hasVPattern,
    confidence: weakResult.confidence.toFixed(2),
    dropAmount: weakResult.dropAmount,
    reboundAmount: weakResult.reboundAmount,
  });
  console.log('✅ 预期：可能不检测到（幅度太小）\n');

  // 测试6：简化接口测试
  console.log('📊 测试6：简化接口测试');
  const simpleResult = isVPattern(vPatternData);
  console.log('简化接口结果:', simpleResult);
  console.log('✅ 预期：应该返回true\n');

  console.log('🎉 V字型模式识别算法测试完成！');
}

// 如果在浏览器环境中，将测试函数挂载到window对象
if (typeof window !== 'undefined') {
  (window as any).testVPatternDetection = testVPatternDetection;
  console.log('🔧 V字型模式识别测试已加载，在控制台运行 testVPatternDetection() 进行测试');
}

export { testVPatternDetection };
