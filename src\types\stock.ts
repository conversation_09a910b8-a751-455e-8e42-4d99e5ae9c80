// 扩展原有的股票类型定义

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
  fromCache?: boolean;
}

// 股票基础信息（扩展）
export interface StockInfo {
  code: string;
  name: string;
  addedAt: string;
  /** 用户自定义排序位置，数值越小越靠前 */
  sortOrder?: number;
}

// K线数据点
export interface KlineDataPoint {
  time: string;
  mainNetInflow: number;
  superLargeNetInflow: number;
  largeNetInflow: number;
  mediumNetInflow: number;
  smallNetInflow: number;
}

// 股票资金流向汇总数据
export interface StockFlowSummary {
  code: string;
  name: string;
  market: number;
  lastUpdate: string;
  mainNetInflow: number;
  superLargeNetInflow: number;
  largeNetInflow: number;
  mediumNetInflow: number;
  smallNetInflow: number;
}

// 股票实时行情数据
export interface StockQuote {
  code: string;
  name: string;
  price: number;           // 最新价
  change: number;          // 涨跌额
  changePercent: number;   // 涨跌幅
  high: number;            // 最高价
  low: number;             // 最低价
  open: number;            // 今开
  preClose: number;        // 昨收
  volume: number;          // 成交量
  amount: number;          // 成交额
}

// 处理后的股票数据
export interface ProcessedStockData {
  summary: StockFlowSummary | null;
  klines: KlineDataPoint[];
  totalCount: number;
  tradePeriods?: any;
}

// 批量股票数据响应
export interface BatchStockDataResponse {
  results: Record<string, ProcessedStockData>;
  errors: Record<string, string>;
  summary: {
    total: number;
    success: number;
    failed: number;
    fromCache: number;
  };
}

// 股票最后更新信息
export interface StockLastUpdate {
  code: string;
  lastUpdate: string | null;
  hasData: boolean;
}

// API服务状态
export interface ApiServiceStatus {
  isHealthy: boolean;
  rateLimitStatus: {
    canMakeRequest: boolean;
    nextAvailableTime: number;
  };
  timestamp: string;
}

// 查询状态枚举
export enum QueryStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 数据加载状态
export interface DataLoadingState {
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isFetching: boolean;
  isRefetching: boolean;
  error: Error | null;
}

// 自动刷新配置
export interface AutoRefreshConfig {
  enabled: boolean;
  interval: number; // 毫秒
  onlyWhenVisible: boolean;
  stopOnError: boolean;
}

// 股票数据查询参数
export interface StockDataQueryParams {
  code: string;
  limit?: number;
  useCache?: boolean;
}

// 批量查询参数
export interface BatchQueryParams {
  codes: string[];
  limit?: number;
  useCache?: boolean;
}

// 错误类型
export interface StockApiError extends Error {
  status?: number;
  code?: string;
  details?: any;
}

// 网络状态
export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  effectiveType?: string;
}

// 缓存信息
export interface CacheInfo {
  lastFetched: string;
  expiresAt: string;
  isStale: boolean;
  source: 'cache' | 'network';
}

// Hook返回类型基础接口
export interface BaseQueryResult<T> extends DataLoadingState {
  data: T | undefined;
  refetch: () => Promise<any>;
  remove: () => void;
}

// 用户标识信息
export interface UserIdentity {
  deviceId: string;
  userId?: string;
  sessionId: string;
}

// 设备信息
export interface DeviceInfo {
  deviceId: string;
  lastSyncTime: string;
  userAgent: string;
  platform?: string;
}

// 用户股票数据
export interface UserStockData {
  userId: string;
  stocks: StockInfo[];
  lastModified: string;
  deviceInfo: DeviceInfo;
  version: number;
}

// 数据同步请求
export interface SyncRequest {
  userIdentity: UserIdentity;
  localStocks: StockInfo[];
  localLastModified: string;
  forceOverwrite?: boolean;
}

// 数据同步响应
export interface SyncResponse {
  success: boolean;
  data: {
    stocks: StockInfo[];
    lastModified: string;
    syncAction: 'local_to_cloud' | 'cloud_to_local' | 'no_change' | 'conflict_resolved';
    conflictResolution?: string;
  };
  message?: string;
}

// 用户数据备份
export interface UserDataBackup {
  exportTime: string;
  version: string;
  userData: UserStockData;
  checksum: string;
}

// 同步状态
export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime: string | null;
  syncInProgress: boolean;
  hasLocalChanges: boolean;
  hasCloudData: boolean;
  conflictDetected: boolean;
}

// 股票数据Hook返回类型
export interface UseStockDataResult extends BaseQueryResult<ProcessedStockData> {
  summary: StockFlowSummary | null;
  klines: KlineDataPoint[];
  lastUpdate: string | null;
  cacheInfo?: CacheInfo;
}

// 批量股票数据Hook返回类型
export interface UseBatchStockDataResult extends BaseQueryResult<BatchStockDataResponse> {
  results: Record<string, ProcessedStockData>;
  errors: Record<string, string>;
  successCount: number;
  errorCount: number;
  fromCacheCount: number;
}

// 股票列表Hook返回类型
export interface UseStockListResult extends BaseQueryResult<StockInfo[]> {
  stocks: StockInfo[];
  addStock: (code: string, name?: string) => Promise<void>;
  removeStock: (code: string) => Promise<void>;
  clearAllStocks: () => Promise<void>;
  addStocksBatch: (stocks: Array<{ code: string; name?: string }>) => Promise<void>;
}

// 自动刷新Hook返回类型
export interface UseAutoRefreshResult {
  isEnabled: boolean;
  interval: number;
  nextRefreshIn: number;
  enable: () => void;
  disable: () => void;
  setInterval: (interval: number) => void;
  refresh: () => void;
}

// API状态Hook返回类型
export interface UseApiStatusResult extends BaseQueryResult<ApiServiceStatus> {
  isHealthy: boolean;
  canMakeRequest: boolean;
  nextAvailableTime: number;
}
