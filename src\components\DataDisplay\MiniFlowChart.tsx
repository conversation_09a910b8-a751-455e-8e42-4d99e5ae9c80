import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { KlineDataPoint } from '@/types/stock';
import { FLOW_COLORS } from '@/utils/chartConfig';
import { isVPattern } from '@/utils/patternDetection';

interface MiniFlowChartProps {
  /** K线数据 */
  klines: KlineDataPoint[];
  /** 图表高度 */
  height?: number;
  /** 是否显示V字型模式高亮 */
  showVPattern?: boolean;
  /** 加载状态 */
  loading?: boolean;
  /** 错误信息 */
  error?: string | null;
  /** 自定义类名 */
  className?: string;
}

/**
 * 迷你资金流向图表组件
 * 轻量级版本，用于监控面板中显示股票资金流入趋势
 */
export const MiniFlowChart: React.FC<MiniFlowChartProps> = ({
  klines = [],
  height = 80,
  showVPattern = false,
  loading = false,
  error = null,
  className = '',
}) => {
  // 检测V字型模式
  const hasVPattern = useMemo(() => {
    if (!showVPattern || klines.length === 0) return false;
    return isVPattern(klines);
  }, [klines, showVPattern]);

  // 图表配置
  const chartOption = useMemo((): EChartsOption => {
    if (klines.length === 0) {
      return {};
    }

    // 提取主力净流入数据
    const mainNetInflowData = klines.map(item => item.mainNetInflow);
    
    // 计算数据范围用于Y轴配置
    const minValue = Math.min(...mainNetInflowData);
    const maxValue = Math.max(...mainNetInflowData);
    const padding = Math.abs(maxValue - minValue) * 0.1; // 10% 的边距

    // 根据V字型模式选择颜色
    const lineColor = hasVPattern ? '#ef4444' : FLOW_COLORS.mainNetInflow; // 红色高亮V字型
    const areaColor = hasVPattern ? 'rgba(239, 68, 68, 0.1)' : 'rgba(255, 107, 157, 0.1)';

    return {
      // 移除所有边距，最大化图表区域
      grid: {
        top: 2,
        right: 2,
        bottom: 2,
        left: 2,
        containLabel: false,
      },
      // 隐藏X轴
      xAxis: {
        type: 'category',
        show: false,
        data: klines.map((_, index) => index),
        boundaryGap: false,
      },
      // 隐藏Y轴
      yAxis: {
        type: 'value',
        show: false,
        min: minValue - padding,
        max: maxValue + padding,
      },
      // 主力净流入趋势线
      series: [
        {
          type: 'line',
          data: mainNetInflowData,
          smooth: true,
          symbol: 'none', // 不显示数据点
          lineStyle: {
            width: hasVPattern ? 3 : 2, // V字型模式使用更粗的线
            color: lineColor,
            type: 'solid',
          },
          areaStyle: {
            color: areaColor,
          },
          animation: false, // 关闭动画以提高性能
        },
      ],
      // 移除工具提示以保持简洁
      tooltip: {
        show: false,
      },
      // 关闭图例
      legend: {
        show: false,
      },
      // 优化性能配置
      animation: false,
      silent: true, // 禁用交互以提高性能
    };
  }, [klines, hasVPattern]);

  // 加载状态
  if (loading) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-50 rounded ${className}`}
        style={{ height }}
      >
        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div 
        className={`flex items-center justify-center bg-red-50 text-red-500 text-xs rounded ${className}`}
        style={{ height }}
      >
        <span>图表加载失败</span>
      </div>
    );
  }

  // 无数据状态
  if (klines.length === 0) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-50 text-gray-400 text-xs rounded ${className}`}
        style={{ height }}
      >
        <span>暂无数据</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ height }}>
      <ReactECharts
        option={chartOption}
        style={{ 
          width: '100%', 
          height: '100%',
        }}
        opts={{
          renderer: 'canvas', // 使用canvas渲染以提高性能
          width: 'auto',
          height: 'auto',
        }}
      />
      {/* V字型模式指示器 */}
      {hasVPattern && (
        <div className="absolute top-1 right-1">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
        </div>
      )}
    </div>
  );
};

export default MiniFlowChart;
