import { EastmoneyApiService } from './eastmoneyApi';
import { CacheService } from './cache';
import { createLogger, CacheStatsLogger } from '../utils/logger';
import { Env, StockInfo } from '../types/api';

/**
 * 定时任务配置
 */
export interface CronConfig {
  enabled: boolean;
  batchSize: number;
  maxRetries: number;
  cacheTtl: number;
  logLevel: string;
}

/**
 * 任务执行结果
 */
export interface TaskResult {
  success: boolean;
  processed: number;
  errors: number;
  duration: number;
  details: {
    successful: string[];
    failed: Array<{ code: string; error: string }>;
  };
}

/**
 * 定时任务服务
 */
export class CronService {
  private apiService: EastmoneyApiService;
  private cacheService: CacheService;
  private configCache: CacheService;
  private logger: ReturnType<typeof createLogger>;
  private cacheStats: CacheStatsLogger;
  private config: CronConfig;

  constructor(env: Env) {
    this.apiService = new EastmoneyApiService();
    this.cacheService = new CacheService(env.STOCK_CACHE);
    this.configCache = new CacheService(env.STOCK_CONFIG);
    this.logger = createLogger('CronService', env);
    this.cacheStats = new CacheStatsLogger(this.logger);
    
    // 解析配置
    this.config = {
      enabled: env.CRON_ENABLED === 'true',
      batchSize: parseInt(env.BATCH_SIZE || '10'),
      maxRetries: parseInt(env.MAX_RETRIES || '3'),
      cacheTtl: parseInt(env.CACHE_TTL || '60'),
      logLevel: env.LOG_LEVEL || 'info',
    };

    this.logger.info('CronService initialized', this.config);
  }

  /**
   * 执行定时任务
   */
  async executeScheduledTask(): Promise<TaskResult> {
    const startTime = Date.now();
    this.logger.info('Starting scheduled task execution');

    if (!this.config.enabled) {
      this.logger.warn('Cron tasks are disabled');
      return {
        success: false,
        processed: 0,
        errors: 0,
        duration: Date.now() - startTime,
        details: { successful: [], failed: [] },
      };
    }

    try {
      // 获取股票列表
      const stocks = await this.getStockList();
      if (stocks.length === 0) {
        this.logger.warn('No stocks found for processing');
        return {
          success: true,
          processed: 0,
          errors: 0,
          duration: Date.now() - startTime,
          details: { successful: [], failed: [] },
        };
      }

      this.logger.info(`Processing ${stocks.length} stocks in batches of ${this.config.batchSize}`);

      // 分批处理股票
      const result = await this.processBatches(stocks);
      
      // 记录缓存统计
      this.cacheStats.logSummary();
      
      const duration = Date.now() - startTime;
      this.logger.info('Scheduled task completed', {
        ...result,
        duration,
      });

      return {
        ...result,
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Scheduled task failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
      });

      return {
        success: false,
        processed: 0,
        errors: 1,
        duration,
        details: {
          successful: [],
          failed: [{ code: 'SYSTEM', error: error instanceof Error ? error.message : 'Unknown error' }],
        },
      };
    }
  }

  /**
   * 获取股票列表
   */
  private async getStockList(): Promise<StockInfo[]> {
    try {
      const cached = await this.configCache.get<StockInfo[]>(CacheService.getStockListKey());
      if (cached) {
        this.cacheStats.hit(CacheService.getStockListKey());
        return cached;
      }

      this.cacheStats.miss(CacheService.getStockListKey());
      this.logger.warn('No stock list found in cache');
      return [];
    } catch (error) {
      this.cacheStats.error(CacheService.getStockListKey(), error as Error);
      this.logger.error('Failed to get stock list', { error: (error as Error).message });
      return [];
    }
  }

  /**
   * 分批处理股票
   */
  private async processBatches(stocks: StockInfo[]): Promise<Omit<TaskResult, 'duration'>> {
    const successful: string[] = [];
    const failed: Array<{ code: string; error: string }> = [];

    // 分批处理
    for (let i = 0; i < stocks.length; i += this.config.batchSize) {
      const batch = stocks.slice(i, i + this.config.batchSize);
      this.logger.info(`Processing batch ${Math.floor(i / this.config.batchSize) + 1}`, {
        batchSize: batch.length,
        codes: batch.map(s => s.code),
      });

      // 并发处理批次内的股票
      const batchPromises = batch.map(stock => this.processStock(stock.code));
      const batchResults = await Promise.allSettled(batchPromises);

      // 处理批次结果
      batchResults.forEach((result, index) => {
        const code = batch[index].code;
        if (result.status === 'fulfilled' && result.value.success) {
          successful.push(code);
        } else {
          const error = result.status === 'rejected' 
            ? result.reason?.message || 'Unknown error'
            : result.value.error || 'Processing failed';
          failed.push({ code, error });
        }
      });

      // 批次间延迟，避免过于频繁的请求
      if (i + this.config.batchSize < stocks.length) {
        await this.delay(1000); // 1秒延迟
      }
    }

    return {
      success: failed.length === 0,
      processed: successful.length,
      errors: failed.length,
      details: { successful, failed },
    };
  }

  /**
   * 处理单个股票
   */
  private async processStock(code: string): Promise<{ success: boolean; error?: string }> {
    let retries = 0;
    
    while (retries <= this.config.maxRetries) {
      try {
        this.logger.debug(`Processing stock ${code} (attempt ${retries + 1})`);
        
        // 获取股票数据
        const result = await this.apiService.getStockFlowData(code, 240);
        
        if (result.success && result.data) {
          // 缓存数据
          const cacheKey = CacheService.getStockDataKey(code);
          await this.cacheService.set(cacheKey, result, this.config.cacheTtl);
          this.cacheStats.set(cacheKey, this.config.cacheTtl);
          
          // 更新最后更新时间
          const updateKey = CacheService.getLastUpdateKey(code);
          await this.cacheService.set(updateKey, new Date().toISOString(), 300);
          this.cacheStats.set(updateKey, 300);
          
          this.logger.debug(`Successfully processed stock ${code}`);
          return { success: true };
        } else {
          throw new Error(result.message || 'API returned unsuccessful result');
        }
      } catch (error) {
        retries++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        if (retries <= this.config.maxRetries) {
          this.logger.warn(`Stock ${code} processing failed, retrying`, {
            attempt: retries,
            error: errorMessage,
          });
          await this.delay(1000 * retries); // 递增延迟
        } else {
          this.logger.error(`Stock ${code} processing failed after ${this.config.maxRetries} retries`, {
            error: errorMessage,
          });
          return { success: false, error: errorMessage };
        }
      }
    }

    return { success: false, error: 'Max retries exceeded' };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(): Promise<{
    enabled: boolean;
    config: CronConfig;
    cacheStats: any;
    lastExecution?: string;
  }> {
    try {
      const lastExecution = await this.cacheService.get<string>('last_cron_execution');
      
      return {
        enabled: this.config.enabled,
        config: this.config,
        cacheStats: this.cacheStats.getStats(),
        lastExecution: lastExecution || undefined,
      };
    } catch (error) {
      this.logger.error('Failed to get task status', { error: (error as Error).message });
      return {
        enabled: this.config.enabled,
        config: this.config,
        cacheStats: this.cacheStats.getStats(),
      };
    }
  }

  /**
   * 手动触发任务
   */
  async triggerManualTask(): Promise<TaskResult> {
    this.logger.info('Manual task trigger requested');
    return this.executeScheduledTask();
  }
}
