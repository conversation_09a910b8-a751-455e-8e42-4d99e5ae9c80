# Cloudflare Pages 快速参考指南

## 🚀 5分钟快速部署

### 1. 准备项目
```bash
# 确保项目有正确的构建脚本
npm run build  # 测试构建是否成功
```

### 2. 推送到 GitHub
```bash
git add .
git commit -m "Ready for Cloudflare Pages deployment"
git push origin main
```

### 3. 连接 Cloudflare Pages
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Pages → Create a project → Connect to Git
3. 选择 GitHub 仓库
4. 配置构建设置：
   - **Framework**: Vite
   - **Build command**: `npm run build`
   - **Build output**: `dist`
5. Save and Deploy

---

## ⚙️ 常用构建配置

### React + Vite
```
Framework preset: Vite
Build command: npm run build
Build output directory: dist
Root directory: (留空)
```

### Vue + Vite
```
Framework preset: Vite
Build command: npm run build
Build output directory: dist
Root directory: (留空)
```

### Next.js (静态导出)
```
Framework preset: Next.js (Static HTML Export)
Build command: npm run build && npm run export
Build output directory: out
Root directory: (留空)
```

### Nuxt.js
```
Framework preset: Nuxt.js
Build command: npm run generate
Build output directory: dist
Root directory: (留空)
```

### Angular
```
Framework preset: Angular
Build command: npm run build
Build output directory: dist/your-app-name
Root directory: (留空)
```

---

## 📁 必需文件模板

### _redirects
```
# SPA 路由支持
/*    /index.html   200

# API 代理（可选）
/api/*  https://your-api.workers.dev/api/:splat  200

# 重定向示例
/old-page  /new-page  301
```

### _headers
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Cache-Control: public, max-age=3600

/assets/*
  Cache-Control: public, max-age=31536000, immutable

/api/*
  Cache-Control: no-cache
```

### package.json 脚本
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:prod": "vite build --mode production",
    "preview": "vite preview"
  }
}
```

---

## 🔧 常见问题快速解决

### 构建失败
```bash
# 清理依赖
rm -rf node_modules package-lock.json
npm install

# 检查 Node.js 版本
node --version  # 推荐 18+
```

### SPA 路由 404
确保 `_redirects` 文件包含：
```
/*    /index.html   200
```

### 环境变量
在 Cloudflare Pages 设置中添加：
```
VITE_API_URL=https://api.example.com
NODE_VERSION=18
```

### 构建超时
在 package.json 中增加内存：
```json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' vite build"
  }
}
```

---

## 🌐 域名配置

### 自定义域名
1. Pages → 项目 → Custom domains
2. Add custom domain
3. 输入域名：`www.example.com`
4. 配置 DNS：
   ```
   Type: CNAME
   Name: www
   Target: your-project.pages.dev
   ```

### SSL 证书
- 自动配置（通常 5-10 分钟）
- 支持通配符证书
- 自动续期

---

## 📊 监控命令

### 部署状态检查
```bash
# 使用 Wrangler CLI
npm install -g wrangler
wrangler pages deployment list --project-name your-project
```

### 本地预览
```bash
# 构建并预览
npm run build
npm run preview

# 或使用 Wrangler
wrangler pages dev dist
```

---

## 🔗 有用链接

- **Dashboard**: https://dash.cloudflare.com/
- **文档**: https://developers.cloudflare.com/pages/
- **状态页**: https://www.cloudflarestatus.com/
- **社区**: https://community.cloudflare.com/

---

## 📋 部署检查清单

**部署前：**
- [ ] `npm run build` 成功
- [ ] `_redirects` 文件存在
- [ ] 环境变量已设置
- [ ] 代码已推送到 GitHub

**部署后：**
- [ ] 网站可访问
- [ ] 路由正常工作
- [ ] 静态资源加载
- [ ] 移动端正常

**优化：**
- [ ] 配置缓存头
- [ ] 启用压缩
- [ ] 设置自定义域名
- [ ] 监控性能指标

---

**💡 提示：** 保存此文件为书签，随时查阅常用配置和解决方案！
