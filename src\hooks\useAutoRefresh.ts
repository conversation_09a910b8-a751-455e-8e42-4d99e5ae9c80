import { useState, useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from 'react-query';
import { QUERY_KEYS, isOnline } from '@/utils/queryClient';
import { UseAutoRefreshResult, AutoRefreshConfig } from '@/types/stock';
import { TIMING_CONFIG } from '@/config/timing';

/**
 * 默认自动刷新配置
 */
const DEFAULT_CONFIG: AutoRefreshConfig = {
  enabled: true,
  interval: TIMING_CONFIG.REFRESH_INTERVALS.GLOBAL_AUTO_REFRESH, // 3分钟（从1分钟调整）
  onlyWhenVisible: true,
  stopOnError: false,
};

/**
 * 自动刷新Hook
 * @param config 刷新配置
 * @param queryKeys 要刷新的查询键数组
 */
export function useAutoRefresh(
  config: Partial<AutoRefreshConfig> = {},
  queryKeys: unknown[][] = []
): UseAutoRefreshResult {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const queryClient = useQueryClient();
  
  const [isEnabled, setIsEnabled] = useState(finalConfig.enabled);
  const [interval, setIntervalState] = useState(finalConfig.interval);
  const [nextRefreshIn, setNextRefreshIn] = useState(0);
  
  const intervalRef = useRef<number | null>(null);
  const countdownRef = useRef<number | null>(null);
  const lastRefreshRef = useRef<number>(0);

  /**
   * 检查页面是否可见
   */
  const isPageVisible = useCallback(() => {
    if (!finalConfig.onlyWhenVisible) return true;
    return !document.hidden;
  }, [finalConfig.onlyWhenVisible]);

  /**
   * 执行刷新
   */
  const refresh = useCallback(() => {
    if (!isOnline()) {
      console.log('离线状态，跳过自动刷新');
      return;
    }

    if (!isPageVisible()) {
      console.log('页面不可见，跳过自动刷新');
      return;
    }

    try {
      // 如果指定了特定的查询键，只刷新这些
      if (queryKeys.length > 0) {
        queryKeys.forEach(queryKey => {
          queryClient.invalidateQueries(queryKey);
        });
      } else {
        // 否则刷新所有股票相关数据
        queryClient.invalidateQueries(QUERY_KEYS.STOCKS);
      }
      
      lastRefreshRef.current = Date.now();
      console.log('自动刷新执行完成');
    } catch (error) {
      console.error('自动刷新失败:', error);
      
      if (finalConfig.stopOnError) {
        setIsEnabled(false);
      }
    }
  }, [queryClient, queryKeys, isPageVisible, finalConfig.stopOnError]);

  /**
   * 启动倒计时
   */
  const startCountdown = useCallback(() => {
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
    }

    const startTime = Date.now();
    
    countdownRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, interval - elapsed);

      setNextRefreshIn(Math.ceil(remaining / 1000));

      if (remaining <= 0) {
        clearInterval(countdownRef.current!);
        countdownRef.current = null;
      }
    }, 1000);
  }, [interval]);

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (!isEnabled || interval <= 0) {
      return;
    }

    // 立即执行一次刷新
    refresh();
    startCountdown();

    // 设置定时器
    intervalRef.current = window.setInterval(() => {
      refresh();
      startCountdown();
    }, interval);

    console.log(`自动刷新已启动，间隔: ${interval}ms`);
  }, [isEnabled, interval, refresh, startCountdown]);

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
    
    setNextRefreshIn(0);
    console.log('自动刷新已停止');
  }, []);

  /**
   * 启用自动刷新
   */
  const enable = useCallback(() => {
    setIsEnabled(true);
  }, []);

  /**
   * 禁用自动刷新
   */
  const disable = useCallback(() => {
    setIsEnabled(false);
  }, []);

  /**
   * 设置刷新间隔
   */
  const setInterval = useCallback((newInterval: number) => {
    if (newInterval < 1000) {
      console.warn('刷新间隔不能小于1秒');
      return;
    }
    setIntervalState(newInterval);
  }, []);

  // 监听启用状态和间隔变化
  useEffect(() => {
    if (isEnabled) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }

    return () => {
      stopAutoRefresh();
    };
  }, [isEnabled, startAutoRefresh, stopAutoRefresh]);

  // 监听页面可见性变化
  useEffect(() => {
    if (!finalConfig.onlyWhenVisible) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('页面隐藏，暂停自动刷新');
        stopAutoRefresh();
      } else if (isEnabled) {
        console.log('页面显示，恢复自动刷新');
        startAutoRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [finalConfig.onlyWhenVisible, isEnabled, startAutoRefresh, stopAutoRefresh]);

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => {
      console.log('网络连接恢复，重启自动刷新');
      if (isEnabled) {
        startAutoRefresh();
      }
    };

    const handleOffline = () => {
      console.log('网络连接断开，停止自动刷新');
      stopAutoRefresh();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isEnabled, startAutoRefresh, stopAutoRefresh]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopAutoRefresh();
    };
  }, [stopAutoRefresh]);

  return {
    isEnabled,
    interval,
    nextRefreshIn,
    enable,
    disable,
    setInterval,
    refresh,
  };
}

/**
 * 股票数据自动刷新Hook
 * @param codes 要刷新的股票代码数组
 * @param config 刷新配置
 */
export function useStockDataAutoRefresh(
  codes: string[] = [],
  config: Partial<AutoRefreshConfig> = {}
) {
  // 构建要刷新的查询键
  const queryKeys = codes.map(code => QUERY_KEYS.STOCK_DATA(code) as unknown as unknown[]);

  return useAutoRefresh(config, queryKeys);
}

/**
 * 全局自动刷新Hook（刷新所有数据）
 * @param config 刷新配置
 */
export function useGlobalAutoRefresh(config: Partial<AutoRefreshConfig> = {}) {
  return useAutoRefresh(config, [QUERY_KEYS.STOCKS as unknown as unknown[]]);
}
