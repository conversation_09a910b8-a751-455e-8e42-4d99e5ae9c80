# 股票资金流向监控

一个现代化的股票资金流向监控网页应用，提供实时的股票资金流向数据分析和可视化图表。

## 功能特性

- 📊 **实时资金流向监控** - 每分钟自动更新股票资金流向数据
- 🎯 **股票代码管理** - 支持添加、删除和管理多个股票代码
- 📈 **数据可视化** - 使用ECharts提供专业的图表展示
- 📱 **响应式设计** - 完美适配桌面和移动设备
- ⚡ **高性能架构** - 基于Cloudflare生态的全球CDN加速
- 🔄 **智能缓存** - 优化的数据缓存策略，提升用户体验

## 技术栈

### 前端
- **React 18** + **TypeScript** - 现代化的前端框架
- **Vite** - 快速的构建工具
- **Tailwind CSS** - 实用优先的CSS框架
- **ECharts** - 专业的数据可视化库
- **React Query** - 强大的数据获取和缓存库

### 后端
- **Cloudflare Workers** - 边缘计算平台
- **Cloudflare KV** - 全球分布式键值存储
- **Hono** - 轻量级Web框架

### 部署
- **Cloudflare Pages** - 前端静态网站托管
- **Cloudflare Workers** - 后端API服务
- **Cron Triggers** - 定时任务调度

## 快速开始

### 🚀 一键部署（推荐）

如果您遇到CORS错误，请运行部署配置向导：

\`\`\`powershell
# Windows PowerShell
.\scripts\setup-deployment.ps1
\`\`\`

或查看详细部署指南：[CORS问题解决指南](docs/CORS_DEPLOYMENT_GUIDE.md)

### 前端开发

1. 安装依赖
\`\`\`bash
npm install
\`\`\`

2. 启动开发服务器
\`\`\`bash
npm run dev
\`\`\`

3. 构建生产版本
\`\`\`bash
npm run build
\`\`\`

### 后端开发

1. 进入workers目录
\`\`\`bash
cd workers
\`\`\`

2. 安装依赖
\`\`\`bash
npm install
\`\`\`

3. 启动本地开发
\`\`\`bash
npm run dev
\`\`\`

4. 部署到Cloudflare
\`\`\`bash
npm run deploy
\`\`\`

### 🔧 CORS问题解决

如果前端无法访问API（CORS错误），说明后端Workers尚未部署：

1. **快速解决**：运行 `.\scripts\setup-deployment.ps1`
2. **手动配置**：查看 [部署指南](docs/CORS_DEPLOYMENT_GUIDE.md)
3. **自动部署**：推送代码到main分支即可自动部署前后端

## 项目结构

\`\`\`
├── src/                    # 前端源码
│   ├── components/         # React组件
│   ├── hooks/             # 自定义Hooks
│   ├── services/          # API服务
│   ├── types/             # TypeScript类型定义
│   └── utils/             # 工具函数
├── workers/               # Cloudflare Workers
│   ├── src/               # Workers源码
│   └── wrangler.toml      # Workers配置
└── public/                # 静态资源
\`\`\`

## 开发指南

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier代码规范
- 组件采用函数式编程风格
- 使用自定义Hooks管理状态逻辑

### 数据流向
1. 前端通过React Query调用后端API
2. Cloudflare Workers处理API请求
3. Workers从东方财富网获取数据
4. 数据缓存到Cloudflare KV
5. 定时任务每分钟更新缓存数据

## 许可证

MIT License

## 免责声明

本项目仅用于学习和研究目的，所有数据来源于公开网站。请遵守相关网站的使用条款，合理使用数据。投资有风险，决策需谨慎。
#   s t o - f u n d 
 
 