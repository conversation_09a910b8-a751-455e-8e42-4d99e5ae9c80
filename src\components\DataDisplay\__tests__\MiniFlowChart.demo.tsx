import React from 'react';
import { MiniFlowChart } from '../MiniFlowChart';
import { KlineDataPoint } from '@/types/stock';

/**
 * 创建测试用的K线数据
 */
function createTestKlineData(values: number[]): KlineDataPoint[] {
  return values.map((value, index) => ({
    time: `2024-01-${String(index + 1).padStart(2, '0')} 09:30:00`,
    mainNetInflow: value,
    superLargeNetInflow: value * 0.4,
    largeNetInflow: value * 0.3,
    mediumNetInflow: value * 0.2,
    smallNetInflow: value * 0.1,
  }));
}

/**
 * 迷你图表组件演示
 */
export const MiniFlowChartDemo: React.FC = () => {
  // 测试数据集
  const normalTrendData = createTestKlineData([
    1000000, 1200000, 1100000, 1300000, 1250000, 1400000, 1350000, 1500000
  ]);

  const vPatternData = createTestKlineData([
    2000000,  // 起始点
    1500000,  // 下跌
    1000000,  // 继续下跌
    500000,   // 最低点
    800000,   // 开始反弹
    1200000,  // 继续反弹
    1600000,  // 强劲反弹
    1800000,  // 持续上涨
  ]);

  const downTrendData = createTestKlineData([
    2000000, 1800000, 1600000, 1400000, 1200000, 1000000, 800000, 600000
  ]);

  const upTrendData = createTestKlineData([
    1000000, 1200000, 1400000, 1600000, 1800000, 2000000, 2200000, 2400000
  ]);

  const emptyData: KlineDataPoint[] = [];

  return (
    <div className="p-6 space-y-6 bg-gray-100 min-h-screen">
      <div className="bg-white rounded-lg p-6">
        <h1 className="text-2xl font-bold mb-6">迷你图表组件演示</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 正常趋势 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">正常趋势</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={normalTrendData}
                showVPattern={true}
              />
            </div>
            <p className="text-xs text-gray-500">普通的资金流入趋势</p>
          </div>

          {/* V字型模式 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">V字型模式</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={vPatternData}
                showVPattern={true}
              />
            </div>
            <p className="text-xs text-gray-500">先跌后涨的V字型模式，应该高亮显示</p>
          </div>

          {/* 下跌趋势 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">下跌趋势</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={downTrendData}
                showVPattern={true}
              />
            </div>
            <p className="text-xs text-gray-500">持续下跌趋势</p>
          </div>

          {/* 上涨趋势 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">上涨趋势</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={upTrendData}
                showVPattern={true}
              />
            </div>
            <p className="text-xs text-gray-500">持续上涨趋势</p>
          </div>

          {/* 加载状态 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">加载状态</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={normalTrendData}
                loading={true}
              />
            </div>
            <p className="text-xs text-gray-500">显示加载动画</p>
          </div>

          {/* 错误状态 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">错误状态</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={normalTrendData}
                error="网络连接失败"
              />
            </div>
            <p className="text-xs text-gray-500">显示错误信息</p>
          </div>

          {/* 无数据状态 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">无数据状态</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={emptyData}
              />
            </div>
            <p className="text-xs text-gray-500">没有数据时的显示</p>
          </div>

          {/* 自定义高度 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">自定义高度 (120px)</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={vPatternData}
                height={120}
                showVPattern={true}
              />
            </div>
            <p className="text-xs text-gray-500">更高的图表显示更多细节</p>
          </div>

          {/* 关闭V字型检测 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">关闭V字型检测</h3>
            <div className="border rounded-lg p-3">
              <MiniFlowChart 
                klines={vPatternData}
                showVPattern={false}
              />
            </div>
            <p className="text-xs text-gray-500">同样的V字型数据，但不高亮显示</p>
          </div>
        </div>

        {/* 批量显示测试 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">批量显示性能测试</h3>
          <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
            {Array.from({ length: 24 }, (_, index) => (
              <div key={index} className="space-y-1">
                <div className="text-xs text-gray-600 text-center">
                  股票{String(index + 1).padStart(3, '0')}
                </div>
                <div className="border rounded">
                  <MiniFlowChart 
                    klines={index % 3 === 0 ? vPatternData : normalTrendData}
                    height={60}
                    showVPattern={true}
                  />
                </div>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            模拟监控面板中的批量显示效果，每3个股票中有1个V字型模式
          </p>
        </div>
      </div>
    </div>
  );
};

// 如果在浏览器环境中，将演示组件挂载到window对象
if (typeof window !== 'undefined') {
  (window as any).MiniFlowChartDemo = MiniFlowChartDemo;
  console.log('🎨 迷你图表演示已加载，在控制台运行 MiniFlowChartDemo 查看组件');
}

export default MiniFlowChartDemo;
