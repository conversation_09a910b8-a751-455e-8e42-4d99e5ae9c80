{"name": "gupiao-zijinliu-workers", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env development", "deploy:prod": "wrangler deploy --env production", "dev": "wrangler dev", "start": "wrangler dev", "tail": "wrangler tail", "tail:prod": "wrangler tail --env production", "kv:list": "wrangler kv:namespace list", "kv:create": "wrangler kv:namespace create", "type-check": "tsc --noEmit", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/workers-types": "^4.20231218.0", "typescript": "^5.2.2", "wrangler": "^3.22.1"}, "dependencies": {"hono": "^3.12.0"}}