import { EastmoneyApiResponse } from '../types/api';

/**
 * 验证股票代码格式
 * @param code 股票代码
 * @returns 是否有效
 */
export function validateStockCode(code: string): boolean {
  if (!code || typeof code !== 'string') {
    return false;
  }
  
  // 6位数字格式
  return /^\d{6}$/.test(code.trim());
}

/**
 * 验证东方财富API响应数据
 * @param data API响应数据
 * @returns 验证结果
 */
export function validateEastmoneyResponse(data: any): data is EastmoneyApiResponse {
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  // 检查基本字段
  if (typeof data.rc !== 'number' || typeof data.rt !== 'number') {
    return false;
  }
  
  // 检查数据字段
  if (!data.data || typeof data.data !== 'object') {
    return false;
  }
  
  const { data: responseData } = data;
  
  // 检查必要字段
  if (!responseData.code || !responseData.name || typeof responseData.market !== 'number') {
    return false;
  }
  
  // 检查klines数据
  if (!Array.isArray(responseData.klines)) {
    return false;
  }
  
  return true;
}

/**
 * 验证资金流向数据行
 * @param kline 单行数据
 * @returns 是否有效
 */
export function validateKlineData(kline: string): boolean {
  if (!kline || typeof kline !== 'string') {
    return false;
  }
  
  const parts = kline.split(',');
  
  // 应该有6个部分：时间,主力净流入,超大单净流入,大单净流入,中单净流入,小单净流入
  if (parts.length !== 6) {
    return false;
  }
  
  // 检查时间格式
  const timePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
  if (!timePattern.test(parts[0])) {
    return false;
  }
  
  // 检查数值格式
  for (let i = 1; i < parts.length; i++) {
    const value = parseFloat(parts[i]);
    if (isNaN(value)) {
      return false;
    }
  }
  
  return true;
}

/**
 * 清洗和标准化股票代码
 * @param code 原始股票代码
 * @returns 标准化后的代码
 */
export function sanitizeStockCode(code: string): string {
  if (!code) return '';
  
  // 去除空格和特殊字符，只保留数字
  const cleaned = code.replace(/\D/g, '');
  
  // 补零到6位
  return cleaned.padStart(6, '0').slice(0, 6);
}

/**
 * 验证数值范围
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否在范围内
 */
export function validateNumberRange(value: number, min: number, max: number): boolean {
  return !isNaN(value) && value >= min && value <= max;
}

/**
 * 验证时间戳格式
 * @param timestamp 时间戳字符串
 * @returns 是否有效
 */
export function validateTimestamp(timestamp: string): boolean {
  if (!timestamp) return false;
  
  const date = new Date(timestamp);
  return !isNaN(date.getTime());
}

/**
 * 数据清洗：移除异常值
 * @param values 数值数组
 * @param threshold 异常值阈值（标准差倍数）
 * @returns 清洗后的数组
 */
export function removeOutliers(values: number[], threshold: number = 3): number[] {
  if (values.length < 3) return values;
  
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  
  return values.filter(val => Math.abs(val - mean) <= threshold * stdDev);
}

/**
 * 验证API响应状态
 * @param response fetch响应对象
 * @returns 验证结果
 */
export function validateApiResponse(response: Response): {
  isValid: boolean;
  error?: string;
} {
  if (!response) {
    return { isValid: false, error: '响应对象为空' };
  }
  
  if (!response.ok) {
    return { 
      isValid: false, 
      error: `HTTP错误: ${response.status} ${response.statusText}` 
    };
  }
  
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    return { 
      isValid: false, 
      error: '响应内容类型不是JSON' 
    };
  }
  
  return { isValid: true };
}

/**
 * 数据完整性检查
 * @param data 要检查的数据
 * @param requiredFields 必需字段列表
 * @returns 检查结果
 */
export function checkDataIntegrity(
  data: any, 
  requiredFields: string[]
): { isComplete: boolean; missingFields: string[] } {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (!(field in data) || data[field] === null || data[field] === undefined) {
      missingFields.push(field);
    }
  }
  
  return {
    isComplete: missingFields.length === 0,
    missingFields
  };
}

/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @returns 解析结果
 */
export function safeJsonParse<T>(jsonString: string): { 
  success: boolean; 
  data?: T; 
  error?: string 
} {
  try {
    const data = JSON.parse(jsonString);
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '未知解析错误' 
    };
  }
}
