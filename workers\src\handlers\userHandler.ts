import { Context } from 'hono';
import { UserService } from '../services/userService';
import { createLogger } from '../utils/logger';
import { 
  ApiResponse, 
  UserIdentity, 
  SyncRequest, 
  SyncResponse, 
  UserDataBackup,
  Env 
} from '../types/api';

/**
 * 用户数据管理API处理器
 */
export class UserHandler {
  private userService: UserService;
  private logger: ReturnType<typeof createLogger>;

  constructor(env: Env) {
    this.userService = new UserService(env);
    this.logger = createLogger('UserHandler');
  }

  /**
   * 生成设备ID
   * POST /api/user/device-id
   */
  async generateDeviceId(c: Context): Promise<Response> {
    try {
      const userAgent = c.req.header('User-Agent') || '';
      const body = await c.req.json().catch(() => ({}));
      
      const deviceId = this.userService.generateDeviceId(userAgent, body.additionalInfo);
      
      return c.json({
        success: true,
        data: { deviceId },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      this.logger.error('Generate device ID failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '生成设备ID失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 同步用户股票数据
   * POST /api/user/sync
   */
  async syncStockData(c: Context): Promise<Response> {
    try {
      const body = await c.req.json() as SyncRequest;
      
      // 验证请求数据
      if (!body.userIdentity || !body.localStocks || !body.localLastModified) {
        return c.json({
          success: false,
          message: '请求参数不完整',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 填充设备信息
      if (body.userIdentity.deviceId && !body.userIdentity.sessionId) {
        const userAgent = c.req.header('User-Agent') || '';
        const identity = this.userService.createUserIdentity(
          body.userIdentity.deviceId,
          body.userIdentity.userId
        );
        body.userIdentity = identity;
      }

      const syncResponse = await this.userService.syncUserStockData(body);
      
      return c.json({
        success: syncResponse.success,
        data: syncResponse.data,
        message: syncResponse.message,
        timestamp: new Date().toISOString(),
      } as ApiResponse<SyncResponse['data']>);
    } catch (error) {
      this.logger.error('Sync stock data failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '数据同步失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取用户股票数据
   * GET /api/user/:userId/stocks
   */
  async getUserStocks(c: Context): Promise<Response> {
    try {
      const userId = c.req.param('userId');
      
      if (!userId) {
        return c.json({
          success: false,
          message: '用户ID不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const userData = await this.userService.getUserStockData(userId);
      
      if (!userData) {
        return c.json({
          success: true,
          data: {
            stocks: [],
            lastModified: new Date().toISOString(),
            version: 0
          },
          message: '用户数据不存在',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }

      return c.json({
        success: true,
        data: {
          stocks: userData.stocks,
          lastModified: userData.lastModified,
          version: userData.version,
          deviceInfo: userData.deviceInfo
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      this.logger.error('Get user stocks failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '获取用户股票数据失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 删除用户数据
   * DELETE /api/user/:userId
   */
  async deleteUserData(c: Context): Promise<Response> {
    try {
      const userId = c.req.param('userId');
      
      if (!userId) {
        return c.json({
          success: false,
          message: '用户ID不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const success = await this.userService.deleteUserData(userId);
      
      return c.json({
        success,
        message: success ? '用户数据删除成功' : '用户数据删除失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      this.logger.error('Delete user data failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '删除用户数据失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 创建数据备份
   * GET /api/user/:userId/backup
   */
  async createBackup(c: Context): Promise<Response> {
    try {
      const userId = c.req.param('userId');
      
      if (!userId) {
        return c.json({
          success: false,
          message: '用户ID不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const backup = await this.userService.createBackup(userId);
      
      if (!backup) {
        return c.json({
          success: false,
          message: '用户数据不存在，无法创建备份',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 404);
      }

      return c.json({
        success: true,
        data: backup,
        timestamp: new Date().toISOString(),
      } as ApiResponse<UserDataBackup>);
    } catch (error) {
      this.logger.error('Create backup failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '创建备份失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 从备份恢复数据
   * POST /api/user/restore
   */
  async restoreFromBackup(c: Context): Promise<Response> {
    try {
      const backup = await c.req.json() as UserDataBackup;
      
      if (!backup || !backup.userData || !backup.checksum) {
        return c.json({
          success: false,
          message: '备份数据格式不正确',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const success = await this.userService.restoreFromBackup(backup);
      
      return c.json({
        success,
        message: success ? '数据恢复成功' : '数据恢复失败，请检查备份文件',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      this.logger.error('Restore from backup failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '数据恢复失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取用户数据统计
   * GET /api/user/:userId/stats
   */
  async getUserStats(c: Context): Promise<Response> {
    try {
      const userId = c.req.param('userId');
      
      if (!userId) {
        return c.json({
          success: false,
          message: '用户ID不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const userData = await this.userService.getUserStockData(userId);
      
      if (!userData) {
        return c.json({
          success: true,
          data: {
            stockCount: 0,
            lastModified: null,
            version: 0,
            deviceInfo: null
          },
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }

      return c.json({
        success: true,
        data: {
          stockCount: userData.stocks.length,
          lastModified: userData.lastModified,
          version: userData.version,
          deviceInfo: userData.deviceInfo
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      this.logger.error('Get user stats failed', { error: (error as Error).message });
      return c.json({
        success: false,
        message: '获取用户统计失败',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }
}
