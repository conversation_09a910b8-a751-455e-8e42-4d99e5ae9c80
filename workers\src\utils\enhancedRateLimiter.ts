/**
 * 增强版频率限制器
 * 扩展现有RateLimiter类，添加自适应延迟机制和错误响应处理
 * 
 * <AUTHOR> System
 * @version 1.0.0
 * @date 2025-01-02
 */

import { RateLimiter, delay } from './retry';

/**
 * 自适应延迟配置接口
 */
export interface AdaptiveDelayConfig {
  /** 初始延迟时间（毫秒） */
  initialDelay: number;
  /** 最大延迟时间（毫秒） */
  maxDelay: number;
  /** 延迟增长因子 */
  growthFactor: number;
  /** 延迟衰减因子 */
  decayFactor: number;
  /** 连续错误阈值 */
  errorThreshold: number;
}

/**
 * 错误统计信息
 */
export interface ErrorStats {
  /** 连续错误次数 */
  consecutiveErrors: number;
  /** 总错误次数 */
  totalErrors: number;
  /** 429错误次数 */
  rateLimitErrors: number;
  /** 最后错误时间 */
  lastErrorTime: number;
  /** 错误率（最近100次请求） */
  errorRate: number;
}

/**
 * 延迟调整记录
 */
export interface DelayAdjustmentLog {
  /** 调整时间 */
  timestamp: number;
  /** 调整前延迟 */
  oldDelay: number;
  /** 调整后延迟 */
  newDelay: number;
  /** 调整原因 */
  reason: string;
  /** 错误信息（如果有） */
  error?: any;
}

/**
 * 增强版频率限制器
 * 继承现有RateLimiter，添加自适应延迟和智能错误处理
 */
export class EnhancedRateLimiter extends RateLimiter {
  private adaptiveDelay: number;
  private readonly config: AdaptiveDelayConfig;
  private errorStats: ErrorStats;
  private adjustmentHistory: DelayAdjustmentLog[] = [];
  private recentRequests: Array<{ timestamp: number; success: boolean }> = [];

  constructor(
    maxRequests: number,
    windowMs: number,
    adaptiveConfig?: Partial<AdaptiveDelayConfig>
  ) {
    super(maxRequests, windowMs);
    
    // 默认自适应延迟配置
    this.config = {
      initialDelay: 1000,      // 1秒初始延迟
      maxDelay: 10000,         // 10秒最大延迟
      growthFactor: 2.0,       // 延迟翻倍增长
      decayFactor: 0.9,        // 延迟衰减10%
      errorThreshold: 3,       // 连续3次错误触发调整
      ...adaptiveConfig
    };

    this.adaptiveDelay = this.config.initialDelay;
    this.errorStats = {
      consecutiveErrors: 0,
      totalErrors: 0,
      rateLimitErrors: 0,
      lastErrorTime: 0,
      errorRate: 0
    };

    this.logDelayAdjustment(0, this.adaptiveDelay, '初始化');
  }

  /**
   * 等待直到可以发送请求，包含自适应延迟
   * @returns Promise<void>
   */
  async waitForSlotWithAdaptiveDelay(): Promise<void> {
    // 先等待基础频率限制
    await this.waitForSlot();
    
    // 再等待自适应延迟
    if (this.adaptiveDelay > 0) {
      console.log(`[EnhancedRateLimiter] 自适应延迟: ${this.adaptiveDelay}ms`);
      await delay(this.adaptiveDelay);
    }
  }

  /**
   * 根据错误调整延迟
   * @param error 错误对象
   */
  adjustDelayOnError(error: any): void {
    const now = Date.now();
    const oldDelay = this.adaptiveDelay;
    
    this.errorStats.consecutiveErrors++;
    this.errorStats.totalErrors++;
    this.errorStats.lastErrorTime = now;

    // 记录请求结果
    this.recordRequestResult(false);

    // 特殊处理429错误
    if (this.isRateLimitError(error)) {
      this.errorStats.rateLimitErrors++;
      this.adaptiveDelay = Math.min(
        this.adaptiveDelay * this.config.growthFactor * 1.5, // 429错误更激进的增长
        this.config.maxDelay
      );
      this.logDelayAdjustment(oldDelay, this.adaptiveDelay, '429错误', error);
    } 
    // 连续错误达到阈值
    else if (this.errorStats.consecutiveErrors >= this.config.errorThreshold) {
      this.adaptiveDelay = Math.min(
        this.adaptiveDelay * this.config.growthFactor,
        this.config.maxDelay
      );
      this.logDelayAdjustment(oldDelay, this.adaptiveDelay, `连续${this.errorStats.consecutiveErrors}次错误`, error);
    }
    // 其他错误适度增加延迟
    else {
      this.adaptiveDelay = Math.min(
        this.adaptiveDelay * 1.2,
        this.config.maxDelay
      );
      this.logDelayAdjustment(oldDelay, this.adaptiveDelay, '一般错误', error);
    }

    // 更新错误率
    this.updateErrorRate();
  }

  /**
   * 成功请求后重置延迟
   */
  resetDelayOnSuccess(): void {
    const oldDelay = this.adaptiveDelay;
    
    // 重置连续错误计数
    this.errorStats.consecutiveErrors = 0;
    
    // 记录请求结果
    this.recordRequestResult(true);
    
    // 逐渐减少延迟
    if (this.adaptiveDelay > this.config.initialDelay) {
      this.adaptiveDelay = Math.max(
        this.adaptiveDelay * this.config.decayFactor,
        this.config.initialDelay
      );
      this.logDelayAdjustment(oldDelay, this.adaptiveDelay, '成功请求衰减');
    }

    // 更新错误率
    this.updateErrorRate();
  }

  /**
   * 判断是否为频率限制错误
   * @param error 错误对象
   * @returns 是否为429错误
   */
  private isRateLimitError(error: any): boolean {
    if (error instanceof Response) {
      return error.status === 429;
    }
    if (error && typeof error === 'object') {
      return error.status === 429 || error.code === 'RATE_LIMIT_EXCEEDED';
    }
    return false;
  }

  /**
   * 记录请求结果
   * @param success 是否成功
   */
  private recordRequestResult(success: boolean): void {
    const now = Date.now();
    this.recentRequests.push({ timestamp: now, success });
    
    // 只保留最近100次请求记录
    if (this.recentRequests.length > 100) {
      this.recentRequests.shift();
    }
  }

  /**
   * 更新错误率
   */
  private updateErrorRate(): void {
    if (this.recentRequests.length === 0) {
      this.errorStats.errorRate = 0;
      return;
    }

    const errors = this.recentRequests.filter(req => !req.success).length;
    this.errorStats.errorRate = errors / this.recentRequests.length;
  }

  /**
   * 记录延迟调整日志
   * @param oldDelay 旧延迟
   * @param newDelay 新延迟
   * @param reason 调整原因
   * @param error 错误信息
   */
  private logDelayAdjustment(
    oldDelay: number,
    newDelay: number,
    reason: string,
    error?: any
  ): void {
    const log: DelayAdjustmentLog = {
      timestamp: Date.now(),
      oldDelay,
      newDelay,
      reason,
      error: error ? this.serializeError(error) : undefined
    };

    this.adjustmentHistory.push(log);
    
    // 只保留最近50次调整记录
    if (this.adjustmentHistory.length > 50) {
      this.adjustmentHistory.shift();
    }

    console.log(`[EnhancedRateLimiter] 延迟调整: ${oldDelay}ms → ${newDelay}ms (${reason})`);
  }

  /**
   * 序列化错误对象用于日志记录
   * @param error 错误对象
   * @returns 序列化后的错误信息
   */
  private serializeError(error: any): any {
    if (error instanceof Response) {
      return {
        status: error.status,
        statusText: error.statusText,
        url: error.url
      };
    }
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }
    return error;
  }

  /**
   * 获取当前自适应延迟
   * @returns 当前延迟时间（毫秒）
   */
  getCurrentDelay(): number {
    return this.adaptiveDelay;
  }

  /**
   * 获取错误统计信息
   * @returns 错误统计
   */
  getErrorStats(): ErrorStats {
    return { ...this.errorStats };
  }

  /**
   * 获取延迟调整历史
   * @returns 调整历史记录
   */
  getAdjustmentHistory(): DelayAdjustmentLog[] {
    return [...this.adjustmentHistory];
  }

  /**
   * 获取健康状态
   * @returns 健康状态信息
   */
  getHealthStatus(): {
    isHealthy: boolean;
    currentDelay: number;
    errorRate: number;
    consecutiveErrors: number;
    recommendation: string;
  } {
    const isHealthy = this.errorStats.errorRate < 0.1 && this.errorStats.consecutiveErrors < 3;
    
    let recommendation = '正常';
    if (this.errorStats.errorRate > 0.3) {
      recommendation = '错误率过高，建议检查API状态';
    } else if (this.errorStats.consecutiveErrors > 5) {
      recommendation = '连续错误过多，建议暂停请求';
    } else if (this.adaptiveDelay > this.config.maxDelay * 0.8) {
      recommendation = '延迟接近上限，建议降低请求频率';
    }

    return {
      isHealthy,
      currentDelay: this.adaptiveDelay,
      errorRate: this.errorStats.errorRate,
      consecutiveErrors: this.errorStats.consecutiveErrors,
      recommendation
    };
  }

  /**
   * 手动重置延迟到初始值
   */
  resetToInitialDelay(): void {
    const oldDelay = this.adaptiveDelay;
    this.adaptiveDelay = this.config.initialDelay;
    this.errorStats.consecutiveErrors = 0;
    this.logDelayAdjustment(oldDelay, this.adaptiveDelay, '手动重置');
  }
}
