# Cloudflare Pages Headers Configuration

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://push2.eastmoney.com https://*.workers.dev; frame-ancestors 'none';
  
  # CORS Headers for API requests
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
  
  # Cache Control
  Cache-Control: public, max-age=3600

# Static Assets Caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# API Proxy (if needed)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# Service Worker
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400
