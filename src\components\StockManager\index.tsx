import { useState } from 'react';
import { CheckCircle, XCircle, Cloud } from 'lucide-react';
import { useStockList } from '@/hooks/useStockList';
import { StockInput } from './StockInput';
import { StockList } from './StockList';
import { CloudStorageModal } from './CloudStorageModal';
import { BatchImportButton } from './BatchImportButton';

interface StockManagerProps {
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  isFullScreen?: boolean;
}

export function StockManager({ onSelectStock, selectedStock, isFullScreen = false }: StockManagerProps) {
  const {
    stocks,
    addStock,
    importFromCSV,
    removeStock,
    batchRemoveStocks,
    reorderStocks,
    isLoading,
    error,
    // 云端存储相关
    syncStatus,
    userIdentity,
    lastModified,
    forceSyncToCloud,
    forceLoadFromCloud,
    createBackup,
    restoreFromBackup,
    deleteCloudData,
    setCustomUserId,
    getUserStats,
  } = useStockList();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [showRealTimeData] = useState(true);
  const [showCloudModal, setShowCloudModal] = useState(false);
  const [selectedStockCodes, setSelectedStockCodes] = useState<Set<string>>(new Set());

  // 显示通知
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // 处理添加股票
  const handleAddStock = async (code: string, name?: string) => {
    const result = await addStock(code, name);

    if (result.success) {
      showNotification('success', result.message || '股票添加成功');
    } else {
      showNotification('error', result.message || '添加失败');
    }

    return result;
  };

  // 处理删除股票
  const handleRemoveStock = (code: string) => {
    removeStock(code);
    showNotification('success', '股票删除成功');

    // 如果删除的是当前选中的股票，清除选中状态
    if (selectedStock === code && onSelectStock) {
      onSelectStock('');
    }
  };

  // 处理批量导入
  const handleBatchImport = async (csvContent: string) => {
    const result = await importFromCSV(csvContent);

    if (result.success) {
      showNotification('success', result.message);
    } else {
      showNotification('error', result.message);
    }

    return result;
  };

  // 处理选择股票
  const handleSelectStock = (code: string, selected: boolean) => {
    setSelectedStockCodes(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(code);
      } else {
        newSet.delete(code);
      }
      return newSet;
    });
  };

  // 处理全选/取消全选
  const handleSelectAll = (selectAll: boolean) => {
    if (selectAll) {
      setSelectedStockCodes(new Set(stocks.map(stock => stock.code)));
    } else {
      setSelectedStockCodes(new Set());
    }
  };

  // 处理批量删除
  const handleBatchRemove = () => {
    const selectedCodes = Array.from(selectedStockCodes);
    if (selectedCodes.length === 0) {
      showNotification('error', '请先选择要删除的股票');
      return;
    }

    const confirmMessage = `确定要删除选中的 ${selectedCodes.length} 个股票吗？`;
    if (window.confirm(confirmMessage)) {
      batchRemoveStocks(selectedCodes);
      setSelectedStockCodes(new Set()); // 清除选择状态
      showNotification('success', `成功删除 ${selectedCodes.length} 个股票`);

      // 如果删除的股票中包含当前选中的股票，清除选中状态
      if (selectedStock && selectedCodes.includes(selectedStock) && onSelectStock) {
        onSelectStock('');
      }
    }
  };

  return (
    <div className={`${isFullScreen ? 'w-full h-full bg-white overflow-hidden flex flex-col' : 'card p-6 h-full'}`}>
      {/* 头部 - 重新设计为单行布局 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'p-6 pb-4' : 'mb-6'}`}>
        {/* 左侧：标题 */}
        <div className="flex-shrink-0">
          <h2 className={`${isFullScreen ? 'text-3xl' : 'text-xl'} font-semibold text-gray-900`}>
            股票管理 {showRealTimeData ? '实时监控股票资金流向' : '添加和管理要监控的股票代码'}
          </h2>
        </div>

        {/* 右侧：股票输入、云端存储按钮、控制按钮 */}
        <div className="flex items-center gap-3">
          {/* 紧凑的股票输入 */}
          <StockInput
            onAddStock={handleAddStock}
            isLoading={isLoading}
            isFullScreen={isFullScreen}
            compact={true}
          />

          {/* 批量导入按钮 */}
          <BatchImportButton
            onImport={handleBatchImport}
            isLoading={isLoading}
          />

          {/* 云端存储按钮 */}
          <button
            onClick={() => setShowCloudModal(true)}
            className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
            title="云端存储管理"
          >
            <Cloud className="w-4 h-4" />
            云端存储
          </button>


        </div>
      </div>

      {/* 通知栏 */}
      {notification && (
        <div className={`
          flex items-center gap-2 p-3 rounded-lg animate-slide-up
          ${isFullScreen ? 'mx-6 mb-4' : 'mb-4'}
          ${notification.type === 'success'
            ? 'bg-success-50 text-success-700 border border-success-200'
            : 'bg-danger-50 text-danger-700 border border-danger-200'
          }
        `}>
          {notification.type === 'success' ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <XCircle className="w-4 h-4" />
          )}
          <span className="text-sm">{notification.message}</span>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className={`flex items-center gap-2 p-3 rounded-lg bg-danger-50 text-danger-700 border border-danger-200 ${isFullScreen ? 'mx-6 mb-4' : 'mb-4'}`}>
          <XCircle className="w-4 h-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}



      {/* 股票列表 */}
      <div className={`flex-1 ${isFullScreen ? 'mx-6 overflow-hidden' : ''}`}>
        <StockList
          stocks={stocks}
          onRemoveStock={handleRemoveStock}
          onSelectStock={onSelectStock}
          selectedStock={selectedStock}
          showRealTimeData={showRealTimeData}
          isFullScreen={isFullScreen}
          onReorderStocks={reorderStocks}
          // 批量选择相关
          selectedStockCodes={selectedStockCodes}
          onSelectStockForBatch={handleSelectStock}
          onSelectAll={handleSelectAll}
          onBatchRemove={handleBatchRemove}
        />
      </div>



      {/* 云端存储弹窗 */}
      <CloudStorageModal
        isOpen={showCloudModal}
        onClose={() => setShowCloudModal(false)}
        syncStatus={syncStatus}
        userIdentity={userIdentity}
        lastModified={lastModified}
        onForceSyncToCloud={forceSyncToCloud}
        onForceLoadFromCloud={forceLoadFromCloud}
        onCreateBackup={createBackup}
        onRestoreFromBackup={restoreFromBackup}
        onDeleteCloudData={deleteCloudData}
        onSetCustomUserId={setCustomUserId}
        onGetUserStats={getUserStats}
      />
    </div>
  );
}

export default StockManager;
