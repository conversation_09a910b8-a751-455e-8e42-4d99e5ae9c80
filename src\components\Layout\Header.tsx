import React, { useState } from 'react';
import { useApiStatus } from '@/hooks/useStockData';
import { useGlobalAutoRefresh } from '@/hooks/useAutoRefresh';
import { CronStatusCard } from '@/components/CronStatus';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  Settings,
  Menu,
  Sun,
  Moon,
  Activity,
  Clock
} from 'lucide-react';

interface HeaderProps {
  /** 是否显示移动端菜单按钮 */
  showMenuButton?: boolean;
  /** 菜单按钮点击事件 */
  onMenuClick?: () => void;
  /** 是否为暗色主题 */
  isDark?: boolean;
  /** 主题切换事件 */
  onThemeToggle?: () => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 页面头部组件
 */
export const Header: React.FC<HeaderProps> = ({
  showMenuButton = false,
  onMenuClick,
  isDark = false,
  onThemeToggle,
  className = '',
}) => {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);
  const [showCronStatus, setShowCronStatus] = useState(false);
  
  // API状态
  const { isHealthy, canMakeRequest, isLoading: statusLoading } = useApiStatus();
  
  // 自动刷新状态 - 默认关闭，避免与股票管理模块的实时监控冲突
  const autoRefresh = useGlobalAutoRefresh({
    enabled: false, // 默认关闭全局自动刷新
    // 使用统一配置的刷新间隔（3分钟，从60秒调整）
    onlyWhenVisible: true,
  });

  // 监听网络状态
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <header className={`bg-white border-b border-gray-200 px-4 py-3 ${className}`}>
      <div className="flex items-center justify-between">
        {/* 左侧：标题和菜单 */}
        <div className="flex items-center gap-4">
          {/* 移动端菜单按钮 */}
          {showMenuButton && (
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="打开菜单"
            >
              <Menu className="w-5 h-5" />
            </button>
          )}
          
          {/* 应用标题 */}
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
              <Activity className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                股票资金流向监控
              </h1>
              <p className="text-xs text-gray-500 hidden sm:block">
                实时监控股票资金流向数据
              </p>
            </div>
          </div>
        </div>

        {/* 右侧：状态指示器和控制按钮 */}
        <div className="flex items-center gap-3">
          {/* 状态指示器 */}
          <div className="flex items-center gap-2">
            {/* 网络状态 */}
            <div className="flex items-center gap-1" title={isOnline ? '网络连接正常' : '网络连接断开'}>
              {isOnline ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              <span className="text-xs text-gray-500 hidden sm:inline">
                {isOnline ? '在线' : '离线'}
              </span>
            </div>

            {/* API状态 */}
            <div className="flex items-center gap-1" title={isHealthy ? 'API服务正常' : 'API服务异常'}>
              {statusLoading ? (
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
              ) : (
                <div className={`w-2 h-2 rounded-full ${isHealthy ? 'bg-green-500' : 'bg-red-500'}`} />
              )}
              <span className="text-xs text-gray-500 hidden sm:inline">
                API
              </span>
            </div>

            {/* 自动刷新状态 */}
            {autoRefresh.isEnabled && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <RefreshCw className="w-3 h-3" />
                <span className="hidden sm:inline">
                  {autoRefresh.nextRefreshIn}s
                </span>
              </div>
            )}
          </div>

          {/* 分隔线 */}
          <div className="w-px h-4 bg-gray-300" />

          {/* 控制按钮 */}
          <div className="flex items-center gap-1">
            {/* 手动刷新 */}
            <button
              onClick={() => autoRefresh.refresh()}
              disabled={!isOnline || !canMakeRequest}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="手动刷新数据"
            >
              <RefreshCw className="w-4 h-4" />
            </button>

            {/* 自动刷新开关 */}
            <button
              onClick={() => autoRefresh.isEnabled ? autoRefresh.disable() : autoRefresh.enable()}
              className={`p-2 rounded-lg transition-colors ${
                autoRefresh.isEnabled
                  ? 'text-primary-600 bg-primary-50 hover:bg-primary-100'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title={autoRefresh.isEnabled ? '停止自动刷新' : '启动自动刷新'}
            >
              <RefreshCw className={`w-4 h-4 ${autoRefresh.isEnabled ? 'animate-spin' : ''}`} />
            </button>

            {/* 主题切换 */}
            {onThemeToggle && (
              <button
                onClick={onThemeToggle}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                title={isDark ? '切换到亮色主题' : '切换到暗色主题'}
              >
                {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </button>
            )}

            {/* 定时任务状态 */}
            <div className="relative">
              <button
                onClick={() => setShowCronStatus(!showCronStatus)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                title="定时任务状态"
              >
                <Clock className="w-4 h-4" />
              </button>

              {/* 定时任务状态弹窗 */}
              {showCronStatus && (
                <>
                  <div
                    className="fixed inset-0 z-40"
                    onClick={() => setShowCronStatus(false)}
                  />
                  <div className="absolute right-0 top-full mt-2 w-96 z-50">
                    <CronStatusCard />
                  </div>
                </>
              )}
            </div>

            {/* 设置按钮 */}
            <button
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="设置"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* API请求限制提示 */}
      {!canMakeRequest && (
        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center gap-2 text-yellow-700 text-sm">
            <RefreshCw className="w-4 h-4" />
            <span>API请求频率限制中，请稍后再试</span>
          </div>
        </div>
      )}
    </header>
  );
};
