# 股票应用API调用架构分析报告

## 概述

当前股票应用使用**两个不同的API端点**来获取不同类型的股票数据，分别服务于不同的业务需求：

1. **股票资金流向数据API** - 用于获取主力资金流入流出的历史数据
2. **股票实时行情数据API** - 用于获取当前价格、涨跌幅等实时行情信息

## API端点详细分析

### 1. 股票资金流向数据API

#### 基本信息
- **API端点路径**: `/api/data/:code`
- **HTTP方法**: GET
- **参数**: 
  - `code`: 股票代码（路径参数）
  - `limit`: 数据条数（查询参数，默认240）
  - `cache`: 是否使用缓存（查询参数，默认true）

#### 后端处理链路
```
前端调用 → stockDataApi.getStockData() 
         → /api/data/:code 
         → DataHandler.getStockData() 
         → EastmoneyApiService.getStockFlowData()
         → 东方财富API
```

#### 后端处理方法
- **路由注册**: `workers/src/index.ts` 第113行
  ```typescript
  app.get('/api/data/:code', async (c) => {
    const handler = c.get('dataHandler') as DataHandler;
    return handler.getStockData(c);
  });
  ```

- **处理器方法**: `workers/src/handlers/data.ts` 第62行
  ```typescript
  async getStockData(c: Context): Promise<Response>
  ```

- **API服务方法**: `workers/src/services/eastmoneyApi.ts` 第30行
  ```typescript
  async getStockFlowData(stockCode: string, limit: number = 240): Promise<ApiResponse<any>>
  ```

#### 东方财富原始API
- **URL构建方法**: `buildFlowDataUrl()`
- **完整URL**: `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get`
- **参数**:
  ```typescript
  {
    secid: `${marketCode}.${stockCode}`,  // 市场代码.股票代码
    klt: '1',                            // 1分钟K线
    lmt: limit.toString(),               // 数据条数
    fields1: 'f1,f2,f3,f7',             // 基础字段
    fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63' // 资金流向字段
  }
  ```

#### 返回数据结构
```typescript
{
  success: boolean,
  data: {
    code: string,           // 股票代码
    name: string,           // 股票名称
    market: number,         // 市场代码
    klines: Array<{
      time: string,                    // 时间
      mainNetInflow: number,          // 主力净流入
      superLargeNetInflow: number,    // 超大单净流入
      largeNetInflow: number,         // 大单净流入
      mediumNetInflow: number,        // 中单净流入
      smallNetInflow: number,         // 小单净流入
    }>
  },
  timestamp: string
}
```

#### 前端调用方式
- **API调用**: `src/services/stockApi.ts` 第160行
  ```typescript
  async getStockData(code: string, limit: number = 240, useCache: boolean = true)
  ```

- **Hook封装**: `src/hooks/useStockData.ts` 第75行
  ```typescript
  useBatchStockData(codes, limit, options)
  ```

- **组件使用**: `src/components/StockManager/StockList.tsx` 第74行
  ```typescript
  const { results, isLoading, isFetching, refetch } = useBatchStockData(stockCodes, 20, {
    refetchInterval: showRealTimeData ? 60000 : undefined,
    enabled: showRealTimeData && stockCodes.length > 0
  });
  ```

---

### 2. 股票实时行情数据API

#### 基本信息
- **API端点路径**: `/api/quote/:code`
- **HTTP方法**: GET
- **参数**: 
  - `code`: 股票代码（路径参数）

#### 后端处理链路
```
前端调用 → stockDataApi.getStockQuote() 
         → /api/quote/:code 
         → DataHandler.getStockQuote() 
         → EastmoneyApiService.getStockQuote()
         → 东方财富API
```

#### 后端处理方法
- **路由注册**: `workers/src/index.ts` 第119行
  ```typescript
  app.get('/api/quote/:code', async (c) => {
    const handler = c.get('dataHandler') as DataHandler;
    return handler.getStockQuote(c);
  });
  ```

- **处理器方法**: `workers/src/handlers/data.ts` 第144行
  ```typescript
  async getStockQuote(c: Context): Promise<Response>
  ```

- **API服务方法**: `workers/src/services/eastmoneyApi.ts` 第265行
  ```typescript
  async getStockQuote(stockCode: string): Promise<ApiResponse<any>>
  ```

#### 东方财富原始API
- **URL构建方法**: `buildQuoteUrl()`
- **完整URL**: `https://push2.eastmoney.com/api/qt/stock/get`
- **参数**:
  ```typescript
  {
    secid: `${marketCode}.${stockCode}`,  // 市场代码.股票代码
    fields: 'f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f57,f58,f169,f170,f171,f172'
  }
  ```

#### 字段映射说明
```typescript
// 价格相关字段（单位：分，需除以100转为元）
f43: 最新价
f44: 最高价  
f45: 最低价
f46: 今开价
f51: 昨收价
f49/f169: 涨跌额

// 涨跌幅字段（需除以100转为百分比）
f50/f170: 涨跌幅

// 成交相关字段
f47: 成交量（股）
f48: 成交额（元）

// 基本信息字段
f57: 股票代码
f58: 股票名称
```

#### 返回数据结构
```typescript
{
  success: boolean,
  data: {
    code: string,           // 股票代码
    name: string,           // 股票名称
    price: number,          // 最新价（元）
    change: number,         // 涨跌额（元）
    changePercent: number,  // 涨跌幅（百分比，如0.29表示0.29%）
    high: number,           // 最高价（元）
    low: number,            // 最低价（元）
    open: number,           // 今开价（元）
    preClose: number,       // 昨收价（元）
    volume: number,         // 成交量（股）
    amount: number,         // 成交额（元）
  },
  timestamp: string
}
```

#### 前端调用方式
- **API调用**: `src/services/stockApi.ts` 第198行
  ```typescript
  async getStockQuote(code: string): Promise<StockQuote>
  ```

- **Hook封装**: `src/hooks/useStockData.ts` 第256行
  ```typescript
  useBatchStockQuotes(codes, options)
  ```

- **组件使用**: `src/components/StockManager/StockList.tsx` 第85行
  ```typescript
  const { data: quotesData } = useBatchStockQuotes(stockCodes, {
    refetchInterval: showRealTimeData ? 60000 : undefined,
    enabled: showRealTimeData && stockCodes.length > 0
  });
  ```

## API调用关系分析

### 1. 两个API的区别

| 特性 | 资金流向API (`/api/data/:code`) | 实时行情API (`/api/quote/:code`) |
|------|--------------------------------|----------------------------------|
| **主要用途** | 获取历史资金流向数据 | 获取实时价格和涨跌幅 |
| **数据类型** | 时间序列数据（K线） | 单点实时数据 |
| **更新频率** | 分钟级历史数据 | 实时数据 |
| **缓存策略** | 1分钟缓存 | 无缓存（实时性要求高） |
| **数据量** | 大（默认240条记录） | 小（单条记录） |
| **东方财富API** | `/api/qt/stock/fflow/kline/get` | `/api/qt/stock/get` |

### 2. 前端组件调用时机

**StockList组件中的双API调用**:
```typescript
// 同时调用两个API
const { results } = useBatchStockData(stockCodes, 20, { ... });        // 资金流向数据
const { data: quotesData } = useBatchStockQuotes(stockCodes, { ... }); // 实时行情数据

// 数据整合
const stocksWithData = useMemo(() => {
  return stocks.map(stock => {
    const data = results[stock.code];           // 资金流向数据
    const quote = quotesData?.results?.[stock.code]; // 行情数据
    
    return {
      ...stock,
      data,                                     // 用于显示资金流向图表
      quote,                                    // 用于显示涨跌幅
      changePercent: quote?.changePercent || 0, // 涨跌幅百分比
    };
  });
}, [stocks, results, quotesData]);
```

### 3. 数据获取流程

1. **初始化阶段**: 组件挂载时，如果`showRealTimeData`为true，同时启动两个API调用
2. **定时刷新**: 每60秒自动刷新两个API的数据
3. **数据整合**: 将资金流向数据和行情数据合并到同一个股票对象中
4. **UI渲染**: 
   - 使用资金流向数据绘制迷你图表
   - 使用行情数据显示涨跌幅和价格信息

## 总结

当前架构采用**双API设计**，很好地分离了不同类型的数据获取：

✅ **优点**:
- 职责分离：资金流向和实时行情各司其职
- 缓存策略不同：资金流向可缓存，行情数据保持实时性
- 数据结构清晰：每个API返回特定领域的数据

✅ **设计合理性**:
- 符合单一职责原则
- 便于独立优化和维护
- 支持不同的刷新频率和缓存策略

这种架构设计确保了应用能够高效地获取和展示股票的完整信息，既有历史趋势分析能力，又有实时行情监控功能。
