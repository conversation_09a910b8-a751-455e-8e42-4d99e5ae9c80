import { Context, Next } from 'hono';

/**
 * CORS配置接口
 */
export interface CorsOptions {
  origin?: string | string[] | ((origin: string) => boolean);
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
  optionsSuccessStatus?: number;
}

/**
 * 默认CORS配置
 */
const DEFAULT_CORS_OPTIONS: CorsOptions = {
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name',
  ],
  exposedHeaders: [],
  credentials: false,
  maxAge: 86400, // 24小时
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

/**
 * 检查origin是否被允许
 * @param origin 请求的origin
 * @param allowedOrigin 允许的origin配置
 * @returns 是否允许
 */
function isOriginAllowed(origin: string, allowedOrigin: string | string[] | ((origin: string) => boolean)): boolean {
  if (typeof allowedOrigin === 'string') {
    return allowedOrigin === '*' || allowedOrigin === origin;
  }
  
  if (Array.isArray(allowedOrigin)) {
    return allowedOrigin.includes(origin) || allowedOrigin.includes('*');
  }
  
  if (typeof allowedOrigin === 'function') {
    return allowedOrigin(origin);
  }
  
  return false;
}

/**
 * 创建CORS中间件
 * @param options CORS配置选项
 * @returns CORS中间件函数
 */
export function createCorsMiddleware(options: CorsOptions = {}) {
  const config = { ...DEFAULT_CORS_OPTIONS, ...options };
  
  return async (c: Context, next: Next) => {
    const origin = c.req.header('Origin') || '';
    const method = c.req.method;
    
    // 检查origin是否被允许
    let allowedOrigin = '';
    if (config.origin) {
      if (isOriginAllowed(origin, config.origin)) {
        allowedOrigin = origin || '*';
      }
    }
    
    // 设置CORS头部
    if (allowedOrigin) {
      c.header('Access-Control-Allow-Origin', allowedOrigin);
    }
    
    if (config.credentials) {
      c.header('Access-Control-Allow-Credentials', 'true');
    }
    
    if (config.exposedHeaders && config.exposedHeaders.length > 0) {
      c.header('Access-Control-Expose-Headers', config.exposedHeaders.join(', '));
    }
    
    // 处理预检请求
    if (method === 'OPTIONS') {
      if (config.methods && config.methods.length > 0) {
        c.header('Access-Control-Allow-Methods', config.methods.join(', '));
      }
      
      if (config.allowedHeaders && config.allowedHeaders.length > 0) {
        c.header('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));
      }
      
      if (config.maxAge) {
        c.header('Access-Control-Max-Age', config.maxAge.toString());
      }
      
      if (config.preflightContinue) {
        await next();
      } else {
        return c.text('', config.optionsSuccessStatus || 204);
      }
    } else {
      await next();
    }
  };
}

/**
 * 预配置的CORS中间件 - 开发环境
 */
export const developmentCors = createCorsMiddleware({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
});

/**
 * 预配置的CORS中间件 - 生产环境
 */
export const productionCors = createCorsMiddleware({
  origin: [
    'https://sto-fund.pages.dev',
    'https://gupiao-zijinliu.pages.dev',
    'https://gupiao-zijinliu.com', // 如果有自定义域名
  ],
  credentials: true,
});

/**
 * 预配置的CORS中间件 - 宽松模式（仅用于测试）
 */
export const permissiveCors = createCorsMiddleware({
  origin: '*',
  credentials: false,
});

/**
 * 根据环境自动选择CORS配置
 * @param environment 环境变量
 * @returns CORS中间件
 */
export function getEnvironmentCors(environment: string = 'development') {
  switch (environment.toLowerCase()) {
    case 'production':
      return productionCors;
    case 'development':
    case 'dev':
      return developmentCors;
    case 'test':
      return permissiveCors;
    default:
      return developmentCors;
  }
}

/**
 * 安全的CORS中间件 - 严格模式
 */
export const secureCors = createCorsMiddleware({
  origin: (origin: string) => {
    // 只允许HTTPS和本地开发
    return origin.startsWith('https://') || 
           origin.startsWith('http://localhost') || 
           origin.startsWith('http://127.0.0.1');
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  maxAge: 3600, // 1小时
});
