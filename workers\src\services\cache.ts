import { ApiResponse, <PERSON>ache<PERSON><PERSON> } from '../types/api';
import { createLogger } from '../utils/logger';

/**
 * 缓存元数据
 */
export interface CacheMetadata {
  createdAt: string;
  expiresAt: string;
  ttl: number;
  version: string;
  size?: number;
}

/**
 * 缓存条目
 */
export interface CacheEntry<T> {
  data: T;
  metadata: CacheMetadata;
}

/**
 * 缓存统计
 */
export interface CacheStatistics {
  totalKeys: number;
  totalSize: number;
  hitRate: number;
  operations: {
    gets: number;
    sets: number;
    deletes: number;
    hits: number;
    misses: number;
  };
}

/**
 * 缓存服务类
 */
export class CacheService {
  private kv: KVNamespace;
  private logger: ReturnType<typeof createLogger>;
  private stats: CacheStatistics['operations'];

  constructor(kv: KVNamespace, source: string = 'CacheService') {
    this.kv = kv;
    this.logger = createLogger(source);
    this.stats = {
      gets: 0,
      sets: 0,
      deletes: 0,
      hits: 0,
      misses: 0,
    };
  }

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @returns 缓存的数据
   */
  async get<T>(key: CacheKey): Promise<T | null> {
    this.stats.gets++;

    try {
      const cached = await this.kv.get(key);
      if (!cached) {
        this.stats.misses++;
        this.logger.debug('Cache miss', { key });
        return null;
      }

      const entry = JSON.parse(cached) as CacheEntry<T>;

      // 检查是否过期
      if (entry.metadata && new Date(entry.metadata.expiresAt) < new Date()) {
        this.stats.misses++;
        this.logger.debug('Cache expired', { key, expiresAt: entry.metadata.expiresAt });
        await this.delete(key); // 清理过期数据
        return null;
      }

      this.stats.hits++;
      this.logger.debug('Cache hit', { key, version: entry.metadata?.version });

      // 返回数据部分，兼容旧格式
      return entry.data !== undefined ? entry.data : (entry as unknown as T);
    } catch (error) {
      this.stats.misses++;
      this.logger.error('Cache get failed', { key, error: (error as Error).message });
      return null;
    }
  }

  /**
   * 设置缓存数据
   * @param key 缓存键
   * @param value 要缓存的数据
   * @param ttl 过期时间（秒）
   */
  async set<T>(key: CacheKey, value: T, ttl: number = 60): Promise<void> {
    this.stats.sets++;

    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + ttl * 1000);

      const entry: CacheEntry<T> = {
        data: value,
        metadata: {
          createdAt: now.toISOString(),
          expiresAt: expiresAt.toISOString(),
          ttl,
          version: '1.0',
          size: JSON.stringify(value).length,
        },
      };

      await this.kv.put(key, JSON.stringify(entry), {
        expirationTtl: ttl,
      });

      this.logger.debug('Cache set', {
        key,
        ttl,
        size: entry.metadata.size,
        expiresAt: entry.metadata.expiresAt
      });
    } catch (error) {
      this.logger.error('Cache set failed', { key, error: (error as Error).message });
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async delete(key: CacheKey): Promise<void> {
    this.stats.deletes++;

    try {
      await this.kv.delete(key);
      this.logger.debug('Cache delete', { key });
    } catch (error) {
      this.logger.error('Cache delete failed', { key, error: (error as Error).message });
    }
  }

  /**
   * 批量删除缓存
   * @param keys 缓存键数组
   */
  async deleteMany(keys: CacheKey[]): Promise<void> {
    try {
      await Promise.all(keys.map(key => this.kv.delete(key)));
    } catch (error) {
      console.error('批量删除缓存失败:', error);
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   * @returns 是否存在
   */
  async exists(key: CacheKey): Promise<boolean> {
    try {
      const value = await this.kv.get(key);
      return value !== null;
    } catch (error) {
      console.error('检查缓存存在性失败:', error);
      return false;
    }
  }

  /**
   * 获取带缓存的数据
   * @param key 缓存键
   * @param fetcher 数据获取函数
   * @param ttl 缓存时间（秒）
   * @returns 数据
   */
  async getOrSet<T>(
    key: CacheKey,
    fetcher: () => Promise<T>,
    ttl: number = 60
  ): Promise<T> {
    // 先尝试从缓存获取
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 缓存不存在，获取新数据
    const data = await fetcher();
    
    // 缓存新数据
    await this.set(key, data, ttl);
    
    return data;
  }

  /**
   * 清空所有缓存（谨慎使用）
   */
  async clear(): Promise<void> {
    try {
      // 注意：KV没有直接的清空方法，这里只是示例
      // 实际使用中需要维护一个键列表或使用命名空间
      console.warn('KV存储没有直接的清空方法，请手动删除特定键');
    } catch (error) {
      console.error('清空缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @param prefix 键前缀
   * @returns 统计信息
   */
  async getStats(prefix?: string): Promise<{
    totalKeys: number;
    estimatedSize: number;
  }> {
    try {
      // KV存储的限制：无法直接获取所有键
      // 这里返回估算值
      return {
        totalKeys: 0,
        estimatedSize: 0,
      };
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return {
        totalKeys: 0,
        estimatedSize: 0,
      };
    }
  }

  /**
   * 生成股票数据缓存键
   * @param code 股票代码
   * @returns 缓存键
   */
  static getStockDataKey(code: string): CacheKey {
    return `stock_data:${code}`;
  }

  /**
   * 生成股票列表缓存键
   * @returns 缓存键
   */
  static getStockListKey(): CacheKey {
    return 'stock_list';
  }

  /**
   * 生成最后更新时间缓存键
   * @param code 股票代码
   * @returns 缓存键
   */
  static getLastUpdateKey(code: string): CacheKey {
    return `last_update:${code}`;
  }

  /**
   * 获取缓存统计信息
   */
  getStatistics(): CacheStatistics['operations'] {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStatistics(): void {
    this.stats = {
      gets: 0,
      sets: 0,
      deletes: 0,
      hits: 0,
      misses: 0,
    };
    this.logger.info('Cache statistics reset');
  }

  /**
   * 获取命中率
   */
  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * 批量设置缓存
   * @param entries 缓存条目数组
   * @param ttl 过期时间（秒）
   */
  async setBatch<T>(entries: Array<{ key: CacheKey; value: T }>, ttl: number = 60): Promise<void> {
    const promises = entries.map(({ key, value }) => this.set(key, value, ttl));
    await Promise.all(promises);
    this.logger.info('Batch cache set completed', { count: entries.length, ttl });
  }

  /**
   * 批量获取缓存
   * @param keys 缓存键数组
   * @returns 缓存数据映射
   */
  async getBatch<T>(keys: CacheKey[]): Promise<Record<string, T | null>> {
    const promises = keys.map(async (key) => {
      const value = await this.get<T>(key);
      return { key, value };
    });

    const results = await Promise.all(promises);
    const resultMap: Record<string, T | null> = {};

    results.forEach(({ key, value }) => {
      resultMap[key] = value;
    });

    this.logger.debug('Batch cache get completed', {
      count: keys.length,
      hits: results.filter(r => r.value !== null).length
    });

    return resultMap;
  }

  /**
   * 检查缓存是否即将过期
   * @param key 缓存键
   * @param thresholdSeconds 阈值秒数
   * @returns 是否即将过期
   */
  async isExpiringSoon(key: CacheKey, thresholdSeconds: number = 60): Promise<boolean> {
    try {
      const cached = await this.kv.get(key);
      if (!cached) return false;

      const entry = JSON.parse(cached) as CacheEntry<any>;
      if (!entry.metadata) return false;

      const expiresAt = new Date(entry.metadata.expiresAt);
      const threshold = new Date(Date.now() + thresholdSeconds * 1000);

      return expiresAt <= threshold;
    } catch (error) {
      this.logger.error('Failed to check expiration', { key, error: (error as Error).message });
      return false;
    }
  }

  /**
   * 预热缓存
   * @param key 缓存键
   * @param fetcher 数据获取函数
   * @param ttl 过期时间（秒）
   */
  async warmup<T>(key: CacheKey, fetcher: () => Promise<T>, ttl: number = 60): Promise<void> {
    try {
      const data = await fetcher();
      await this.set(key, data, ttl);
      this.logger.info('Cache warmed up', { key, ttl });
    } catch (error) {
      this.logger.error('Cache warmup failed', { key, error: (error as Error).message });
    }
  }
}
