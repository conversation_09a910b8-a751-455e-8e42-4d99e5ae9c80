import { KlineDataPoint } from '@/types/stock';

/**
 * V字型模式检测配置
 */
export interface VPatternConfig {
  /** 最小数据点数量 */
  minDataPoints: number;
  /** 最小下跌幅度（绝对值，单位：元） */
  minDropAmount: number;
  /** 最小反弹幅度（绝对值，单位：元） */
  minReboundAmount: number;
  /** 最小下跌比例（相对于起始值的百分比） */
  minDropRatio: number;
  /** 最小反弹比例（相对于最低点的百分比） */
  minReboundRatio: number;
  /** 分析的数据点数量 */
  analysisWindow: number;
}

/**
 * 默认V字型模式检测配置
 */
export const DEFAULT_V_PATTERN_CONFIG: VPatternConfig = {
  minDataPoints: 8,
  minDropAmount: 500000, // 50万
  minReboundAmount: 300000, // 30万
  minDropRatio: 0.1, // 10%
  minReboundRatio: 0.15, // 15%
  analysisWindow: 12,
};

/**
 * V字型模式检测结果
 */
export interface VPatternResult {
  /** 是否检测到V字型模式 */
  hasVPattern: boolean;
  /** 置信度 (0-1) */
  confidence: number;
  /** 下跌幅度 */
  dropAmount: number;
  /** 反弹幅度 */
  reboundAmount: number;
  /** 下跌比例 */
  dropRatio: number;
  /** 反弹比例 */
  reboundRatio: number;
  /** 最低点索引 */
  bottomIndex: number;
  /** 分析的数据点 */
  analysisData: KlineDataPoint[];
}

/**
 * 检测V字型资金流入模式
 * 
 * V字型模式特征：
 * 1. 资金净流入先大幅下跌
 * 2. 达到最低点后开始反弹
 * 3. 反弹幅度达到一定标准
 * 
 * @param klines K线数据数组
 * @param config 检测配置
 * @returns V字型模式检测结果
 */
export function detectVPattern(
  klines: KlineDataPoint[],
  config: Partial<VPatternConfig> = {}
): VPatternResult {
  const finalConfig = { ...DEFAULT_V_PATTERN_CONFIG, ...config };
  
  // 初始化结果
  const result: VPatternResult = {
    hasVPattern: false,
    confidence: 0,
    dropAmount: 0,
    reboundAmount: 0,
    dropRatio: 0,
    reboundRatio: 0,
    bottomIndex: -1,
    analysisData: [],
  };

  // 检查数据点数量
  if (klines.length < finalConfig.minDataPoints) {
    return result;
  }

  // 获取最近的数据点进行分析
  const analysisData = klines.slice(-finalConfig.analysisWindow);
  result.analysisData = analysisData;

  if (analysisData.length < finalConfig.minDataPoints) {
    return result;
  }

  // 提取主力净流入数据
  const netInflows = analysisData.map(point => point.mainNetInflow);
  
  // 找到最低点
  const minValue = Math.min(...netInflows);
  const bottomIndex = netInflows.indexOf(minValue);
  result.bottomIndex = bottomIndex;

  // 需要最低点不在首尾位置，确保有下跌和反弹的空间
  if (bottomIndex <= 1 || bottomIndex >= netInflows.length - 2) {
    return result;
  }

  // 计算下跌阶段（从开始到最低点）
  const startValue = netInflows[0];
  const bottomValue = netInflows[bottomIndex];
  const dropAmount = startValue - bottomValue;
  const dropRatio = startValue !== 0 ? Math.abs(dropAmount / startValue) : 0;

  result.dropAmount = dropAmount;
  result.dropRatio = dropRatio;

  // 计算反弹阶段（从最低点到结束）
  const endValue = netInflows[netInflows.length - 1];
  const reboundAmount = endValue - bottomValue;
  const reboundRatio = bottomValue !== 0 ? Math.abs(reboundAmount / bottomValue) : 0;

  result.reboundAmount = reboundAmount;
  result.reboundRatio = reboundRatio;

  // 检查是否满足V字型模式条件
  const meetsDropAmount = dropAmount >= finalConfig.minDropAmount;
  const meetsReboundAmount = reboundAmount >= finalConfig.minReboundAmount;
  const meetsDropRatio = dropRatio >= finalConfig.minDropRatio;
  const meetsReboundRatio = reboundRatio >= finalConfig.minReboundRatio;

  // 必须同时满足绝对值和相对值条件
  const hasVPattern = (meetsDropAmount || meetsDropRatio) && 
                      (meetsReboundAmount || meetsReboundRatio) &&
                      dropAmount > 0 && reboundAmount > 0;

  result.hasVPattern = hasVPattern;

  // 计算置信度
  if (hasVPattern) {
    let confidence = 0;
    
    // 基于下跌幅度的置信度
    const dropConfidence = Math.min(dropRatio / finalConfig.minDropRatio, 1) * 0.4;
    
    // 基于反弹幅度的置信度
    const reboundConfidence = Math.min(reboundRatio / finalConfig.minReboundRatio, 1) * 0.4;
    
    // 基于形状对称性的置信度
    const symmetryRatio = Math.min(reboundAmount / dropAmount, dropAmount / reboundAmount);
    const symmetryConfidence = symmetryRatio * 0.2;
    
    confidence = dropConfidence + reboundConfidence + symmetryConfidence;
    result.confidence = Math.min(confidence, 1);
  }

  return result;
}

/**
 * 批量检测多只股票的V字型模式
 * 
 * @param stocksData 股票数据映射 { 股票代码: K线数据 }
 * @param config 检测配置
 * @returns V字型模式检测结果映射
 */
export function detectVPatternBatch(
  stocksData: Record<string, KlineDataPoint[]>,
  config: Partial<VPatternConfig> = {}
): Record<string, VPatternResult> {
  const results: Record<string, VPatternResult> = {};
  
  Object.entries(stocksData).forEach(([stockCode, klines]) => {
    results[stockCode] = detectVPattern(klines, config);
  });
  
  return results;
}

/**
 * 获取V字型股票列表（按置信度排序）
 * 
 * @param stocksData 股票数据映射
 * @param config 检测配置
 * @returns 按置信度降序排列的V字型股票列表
 */
export function getVPatternStocks(
  stocksData: Record<string, KlineDataPoint[]>,
  config: Partial<VPatternConfig> = {}
): Array<{ stockCode: string; result: VPatternResult }> {
  const results = detectVPatternBatch(stocksData, config);
  
  return Object.entries(results)
    .filter(([_, result]) => result.hasVPattern)
    .map(([stockCode, result]) => ({ stockCode, result }))
    .sort((a, b) => b.result.confidence - a.result.confidence);
}

/**
 * 简化的V字型检测函数（向后兼容）
 * 
 * @param klines K线数据数组
 * @param config 简化配置
 * @returns 是否检测到V字型模式
 */
export function isVPattern(
  klines: KlineDataPoint[],
  config: { minDrop?: number; minRebound?: number } = {}
): boolean {
  const vConfig: Partial<VPatternConfig> = {
    minDropAmount: config.minDrop || DEFAULT_V_PATTERN_CONFIG.minDropAmount,
    minReboundAmount: config.minRebound || DEFAULT_V_PATTERN_CONFIG.minReboundAmount,
  };
  
  return detectVPattern(klines, vConfig).hasVPattern;
}
