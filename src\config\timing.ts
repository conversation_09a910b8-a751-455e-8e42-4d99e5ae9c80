/**
 * 统一时间配置管理系统
 * 集中管理所有刷新间隔、API延迟和缓存TTL配置
 * 
 * <AUTHOR> System
 * @version 1.0.0
 * @date 2025-01-02
 */

/**
 * 刷新间隔配置接口
 */
export interface RefreshIntervals {
  /** 股票数据刷新间隔（毫秒） */
  STOCK_DATA: number;
  /** 批量股票数据刷新间隔（毫秒） */
  STOCK_DATA_BATCH: number;
  /** API状态检查间隔（毫秒） */
  API_STATUS: number;
  /** 股票列表刷新间隔（毫秒） */
  STOCK_LIST: number;
  /** 全局自动刷新间隔（毫秒） */
  GLOBAL_AUTO_REFRESH: number;
}

/**
 * API调用延迟配置接口
 */
export interface ApiDelays {
  /** API请求之间的延迟（毫秒） */
  BETWEEN_REQUESTS: number;
  /** 频率限制器检查间隔（毫秒） */
  RATE_LIMITER_CHECK: number;
  /** 重试基础延迟（毫秒） */
  RETRY_BASE: number;
  /** 重试最大延迟（毫秒） */
  RETRY_MAX: number;
  /** 自适应延迟初始值（毫秒） */
  ADAPTIVE_INITIAL: number;
  /** 自适应延迟最大值（毫秒） */
  ADAPTIVE_MAX: number;
}

/**
 * 缓存TTL配置接口
 */
export interface CacheTtl {
  /** 股票数据缓存时间（秒） */
  STOCK_DATA: number;
  /** 最后更新时间缓存（秒） */
  LAST_UPDATE: number;
  /** 股票行情缓存时间（秒） */
  STOCK_QUOTE: number;
  /** 批量数据缓存时间（秒） */
  BATCH_DATA: number;
  /** 股票列表缓存时间（秒） */
  STOCK_LIST: number;
}

/**
 * React Query缓存配置接口
 */
export interface QueryCacheConfig {
  /** 数据新鲜时间（毫秒） */
  staleTime: number;
  /** 缓存保持时间（毫秒） */
  cacheTime: number;
}

/**
 * 完整时间配置接口
 */
export interface TimingConfig {
  REFRESH_INTERVALS: RefreshIntervals;
  API_DELAYS: ApiDelays;
  CACHE_TTL: CacheTtl;
  QUERY_CACHE: {
    STOCK_LIST: QueryCacheConfig;
    STOCK_DATA: QueryCacheConfig;
    STOCK_DATA_BATCH: QueryCacheConfig;
    API_STATUS: QueryCacheConfig;
  };
}

/**
 * 统一时间配置
 * 
 * 主要优化：
 * 1. 刷新间隔从60秒调整为180秒（3分钟）
 * 2. API调用间隔从200ms增加到1000ms
 * 3. 添加自适应延迟机制
 */
export const TIMING_CONFIG: TimingConfig = {
  // 刷新间隔配置
  REFRESH_INTERVALS: {
    STOCK_DATA: 180000,           // 3分钟（从60秒调整）
    STOCK_DATA_BATCH: 180000,     // 3分钟（从60秒调整）
    API_STATUS: 30000,            // 30秒（保持不变）
    STOCK_LIST: 30 * 60 * 1000,   // 30分钟（保持不变）
    GLOBAL_AUTO_REFRESH: 180000,  // 3分钟（从60秒调整）
  },

  // API调用延迟配置
  API_DELAYS: {
    BETWEEN_REQUESTS: 1000,       // 1秒（从200ms调整）
    RATE_LIMITER_CHECK: 100,      // 100ms（保持不变）
    RETRY_BASE: 1000,             // 1秒（保持不变）
    RETRY_MAX: 30000,             // 30秒（保持不变）
    ADAPTIVE_INITIAL: 1000,       // 自适应延迟初始值1秒
    ADAPTIVE_MAX: 10000,          // 自适应延迟最大值10秒
  },

  // 缓存TTL配置（秒）
  CACHE_TTL: {
    STOCK_DATA: 60,               // 股票数据缓存60秒
    LAST_UPDATE: 300,             // 更新时间缓存5分钟
    STOCK_QUOTE: 30,              // 行情数据缓存30秒
    BATCH_DATA: 60,               // 批量数据缓存60秒
    STOCK_LIST: 1800,             // 股票列表缓存30分钟
  },

  // React Query缓存配置
  QUERY_CACHE: {
    STOCK_LIST: {
      staleTime: 30 * 60 * 1000,  // 30分钟
      cacheTime: 60 * 60 * 1000,  // 1小时
    },
    STOCK_DATA: {
      staleTime: 3 * 60 * 1000,   // 3分钟（从1分钟调整）
      cacheTime: 10 * 60 * 1000,  // 10分钟
    },
    STOCK_DATA_BATCH: {
      staleTime: 3 * 60 * 1000,   // 3分钟（从1分钟调整）
      cacheTime: 10 * 60 * 1000,  // 10分钟
    },
    API_STATUS: {
      staleTime: 30 * 1000,       // 30秒
      cacheTime: 2 * 60 * 1000,   // 2分钟
    },
  },
};

/**
 * 配置验证函数
 */
export class TimingConfigValidator {
  /**
   * 验证刷新间隔配置
   */
  static validateRefreshIntervals(intervals: RefreshIntervals): boolean {
    const minInterval = 1000; // 最小1秒
    const maxInterval = 24 * 60 * 60 * 1000; // 最大24小时

    return Object.values(intervals).every(interval => 
      typeof interval === 'number' && 
      interval >= minInterval && 
      interval <= maxInterval
    );
  }

  /**
   * 验证API延迟配置
   */
  static validateApiDelays(delays: ApiDelays): boolean {
    const minDelay = 0;
    const maxDelay = 60 * 1000; // 最大60秒

    return Object.values(delays).every(delay => 
      typeof delay === 'number' && 
      delay >= minDelay && 
      delay <= maxDelay
    );
  }

  /**
   * 验证缓存TTL配置
   */
  static validateCacheTtl(ttl: CacheTtl): boolean {
    const minTtl = 1; // 最小1秒
    const maxTtl = 24 * 60 * 60; // 最大24小时

    return Object.values(ttl).every(value => 
      typeof value === 'number' && 
      value >= minTtl && 
      value <= maxTtl
    );
  }

  /**
   * 验证完整配置
   */
  static validateConfig(config: TimingConfig): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!this.validateRefreshIntervals(config.REFRESH_INTERVALS)) {
      errors.push('刷新间隔配置无效');
    }

    if (!this.validateApiDelays(config.API_DELAYS)) {
      errors.push('API延迟配置无效');
    }

    if (!this.validateCacheTtl(config.CACHE_TTL)) {
      errors.push('缓存TTL配置无效');
    }

    // 验证逻辑关系
    if (config.API_DELAYS.RETRY_BASE > config.API_DELAYS.RETRY_MAX) {
      errors.push('重试基础延迟不能大于最大延迟');
    }

    if (config.API_DELAYS.ADAPTIVE_INITIAL > config.API_DELAYS.ADAPTIVE_MAX) {
      errors.push('自适应初始延迟不能大于最大延迟');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

/**
 * 获取环境变量中的刷新间隔，如果未设置则使用默认值
 */
export function getRefreshIntervalFromEnv(): number {
  if (typeof window !== 'undefined') {
    // 浏览器环境
    const envInterval = import.meta.env?.VITE_REFRESH_INTERVAL;
    if (envInterval && !isNaN(Number(envInterval))) {
      return Number(envInterval);
    }
  }
  
  return TIMING_CONFIG.REFRESH_INTERVALS.STOCK_DATA;
}

/**
 * 创建基于配置的延迟函数
 */
export function createDelay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 格式化时间间隔为可读字符串
 */
export function formatInterval(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60 * 1000) {
    return `${Math.round(ms / 1000)}秒`;
  } else if (ms < 60 * 60 * 1000) {
    return `${Math.round(ms / (60 * 1000))}分钟`;
  } else {
    return `${Math.round(ms / (60 * 60 * 1000))}小时`;
  }
}

// 导出默认配置实例
export default TIMING_CONFIG;

// 在开发环境下验证配置（兼容浏览器和Node.js环境）
function validateConfigInDevelopment() {
  // 检查是否为开发环境
  let isDevelopment = false;

  try {
    // 浏览器环境检查（Vite）- 优先检查
    if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.DEV) {
      isDevelopment = true;
    }
    // 浏览器环境检查（开发服务器）
    else if (typeof window !== 'undefined' && window.location &&
             (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
      isDevelopment = true;
    }
    // 简单的开发环境标识检查
    else if (typeof window !== 'undefined' && window.location && window.location.port === '3000') {
      isDevelopment = true;
    }
  } catch (error) {
    // 忽略环境检查错误，默认不执行验证
    return;
  }

  if (isDevelopment) {
    const validation = TimingConfigValidator.validateConfig(TIMING_CONFIG);
    if (!validation.isValid) {
      console.warn('时间配置验证失败:', validation.errors);
    } else {
      console.log('时间配置验证通过');
    }
  }
}

// 执行配置验证
validateConfigInDevelopment();
