import { RequestHeaders } from '../types/api';

/**
 * User-Agent 列表 - 轮换使用以避免被识别
 */
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
];

/**
 * Referer 列表 - 模拟从不同页面访问
 */
const REFERERS = [
  'https://data.eastmoney.com/',
  'https://data.eastmoney.com/zjlx/',
  'https://data.eastmoney.com/zjlx/detail.html',
  'https://quote.eastmoney.com/',
];

/**
 * 获取随机User-Agent
 */
export function getRandomUserAgent(): string {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

/**
 * 获取随机Referer
 */
export function getRandomReferer(): string {
  return REFERERS[Math.floor(Math.random() * REFERERS.length)];
}

/**
 * 生成标准请求头
 * @param options 可选的请求头配置
 * @returns 请求头对象
 */
export function generateHeaders(options: RequestHeaders = {}): RequestHeaders {
  const defaultHeaders: RequestHeaders = {
    'User-Agent': getRandomUserAgent(),
    'Referer': getRandomReferer(),
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
  };

  return { ...defaultHeaders, ...options };
}

/**
 * 为东方财富API生成专用请求头
 * @param stockCode 股票代码（可选）
 * @returns 请求头对象
 */
export function generateEastmoneyHeaders(stockCode?: string): RequestHeaders {
  const baseHeaders = generateHeaders();
  
  // 如果有股票代码，设置更具体的Referer
  if (stockCode) {
    baseHeaders.Referer = `https://data.eastmoney.com/zjlx/${stockCode}.html`;
  }

  return {
    ...baseHeaders,
    'Accept': '*/*',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
  };
}

/**
 * 检查请求头是否有效
 * @param headers 请求头对象
 * @returns 是否有效
 */
export function validateHeaders(headers: Record<string, string>): boolean {
  const requiredHeaders = ['User-Agent', 'Accept'];
  return requiredHeaders.every(header => headers[header]);
}

/**
 * 添加随机延迟头部（模拟人类行为）
 * @param headers 原始请求头
 * @returns 添加了随机特征的请求头
 */
export function addRandomFeatures(headers: RequestHeaders): RequestHeaders {
  const features: RequestHeaders = {};

  // 随机添加一些可选头部
  if (Math.random() > 0.5) {
    features['DNT'] = '1';
  }

  if (Math.random() > 0.7) {
    features['Upgrade-Insecure-Requests'] = '1';
  }

  return { ...headers, ...features };
}

/**
 * 获取请求头指纹（用于缓存和去重）
 * @param headers 请求头对象
 * @returns 指纹字符串
 */
export function getHeadersFingerprint(headers: RequestHeaders): string {
  const key = `${headers['User-Agent']}_${headers['Referer']}`;
  return btoa(key).slice(0, 16);
}
