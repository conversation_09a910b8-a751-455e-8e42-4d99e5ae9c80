import React, { useState, useMemo } from 'react';
import { KlineDataPoint, StockFlowSummary } from '@/types/stock';
import {
  formatMoneyAuto,
  formatTime,
  getFlowColor,
  getFlowDirection
} from '@/utils/formatters';
import { ChevronUp, ChevronDown, Download, Search } from 'lucide-react';

interface DataTableProps {
  /** K线数据 */
  klines: KlineDataPoint[];
  /** 汇总数据 */
  summary: StockFlowSummary | null;
  /** 每页显示条数 */
  pageSize?: number;
  /** 是否显示搜索 */
  showSearch?: boolean;
  /** 是否显示导出 */
  showExport?: boolean;
  /** 自定义类名 */
  className?: string;
}

type SortField = keyof KlineDataPoint;
type SortOrder = 'asc' | 'desc';

/**
 * 数据表格组件
 */
export const DataTable: React.FC<DataTableProps> = ({
  klines = [],
  summary = null,
  pageSize = 20,
  showSearch = true,
  showExport = true,
  className = '',
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<SortField>('time');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // 表格列配置
  const columns = [
    { key: 'time', label: '时间', sortable: true, width: '120px' },
    { key: 'mainNetInflow', label: '主力净流入', sortable: true, width: '120px' },
    { key: 'superLargeNetInflow', label: '超大单净流入', sortable: true, width: '130px' },
    { key: 'largeNetInflow', label: '大单净流入', sortable: true, width: '120px' },
    { key: 'mediumNetInflow', label: '中单净流入', sortable: true, width: '120px' },
    { key: 'smallNetInflow', label: '小单净流入', sortable: true, width: '120px' },
  ] as const;

  // 过滤和排序数据
  const filteredAndSortedData = useMemo(() => {
    let filtered = klines;

    // 搜索过滤
    if (searchTerm) {
      filtered = klines.filter(item =>
        formatTime(item.time, 'datetime').includes(searchTerm)
      );
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (sortField === 'time') {
        aValue = new Date(aValue as string).getTime();
        bValue = new Date(bValue as string).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [klines, searchTerm, sortField, sortOrder]);

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAndSortedData.slice(startIndex, endIndex);
  }, [filteredAndSortedData, currentPage, pageSize]);

  // 总页数
  const totalPages = Math.ceil(filteredAndSortedData.length / pageSize);

  // 处理排序
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
    setCurrentPage(1);
  };

  // 导出数据
  const handleExport = () => {
    const headers = columns.map(col => col.label).join(',');
    const rows = filteredAndSortedData.map(item => {
      return columns.map(col => {
        if (col.key === 'time') {
          return formatTime(item[col.key], 'datetime');
        } else {
          return formatMoneyAuto(item[col.key] as number);
        }
      }).join(',');
    });

    const csvContent = [headers, ...rows].join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `资金流向数据_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
  };

  // 渲染排序图标
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronUp className="w-4 h-4 text-gray-400" />;
    }
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4 text-primary-600" /> : 
      <ChevronDown className="w-4 h-4 text-primary-600" />;
  };

  // 渲染数值单元格
  const renderValueCell = (value: number) => {
    const formattedValue = formatMoneyAuto(value);
    const color = getFlowColor(value);
    const direction = getFlowDirection(value);

    return (
      <span style={{ color }} className="font-medium">
        {direction === '平' ? formattedValue : `${direction} ${formattedValue}`}
      </span>
    );
  };

  if (klines.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-8 text-center ${className}`}>
        <div className="text-gray-400 mb-2">
          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <p className="text-gray-600">暂无数据</p>
        <p className="text-gray-500 text-sm mt-1">请选择股票查看详细数据</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border ${className}`}>
      {/* 表格头部 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">资金流向明细</h3>
            {summary && (
              <p className="text-sm text-gray-500 mt-1">
                {summary.name} ({summary.code}) - 共 {filteredAndSortedData.length} 条记录
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {/* 搜索框 */}
            {showSearch && (
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索时间..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            )}
            
            {/* 导出按钮 */}
            {showExport && (
              <button
                onClick={handleExport}
                className="flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm"
              >
                <Download className="w-4 h-4" />
                导出CSV
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 表格内容 */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center gap-1">
                    {column.label}
                    {column.sortable && renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((item, index) => (
              <tr key={`${item.time}-${index}`} className="hover:bg-gray-50">
                <td className="px-4 py-3 text-sm text-gray-900">
                  {formatTime(item.time, 'datetime')}
                </td>
                <td className="px-4 py-3 text-sm">
                  {renderValueCell(item.mainNetInflow)}
                </td>
                <td className="px-4 py-3 text-sm">
                  {renderValueCell(item.superLargeNetInflow)}
                </td>
                <td className="px-4 py-3 text-sm">
                  {renderValueCell(item.largeNetInflow)}
                </td>
                <td className="px-4 py-3 text-sm">
                  {renderValueCell(item.mediumNetInflow)}
                </td>
                <td className="px-4 py-3 text-sm">
                  {renderValueCell(item.smallNetInflow)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t bg-gray-50 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, filteredAndSortedData.length)} 条，
            共 {filteredAndSortedData.length} 条记录
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
            >
              上一页
            </button>
            
            <span className="text-sm text-gray-700">
              第 {currentPage} / {totalPages} 页
            </span>
            
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
