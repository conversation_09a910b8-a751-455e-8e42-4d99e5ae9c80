import React, { useState } from 'react';
import {
  Cloud,
  CloudOff,
  Download,
  Upload,
  Settings,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Trash2
} from 'lucide-react';
import { UserDataBackup } from '@/types/stock';

interface CloudStorageManagerProps {
  syncStatus: any;
  userIdentity: any;
  lastModified: string;
  onForceSyncToCloud: () => Promise<boolean>;
  onForceLoadFromCloud: () => Promise<boolean>;
  onCreateBackup: () => Promise<UserDataBackup | null>;
  onRestoreFromBackup: (backup: UserDataBackup) => Promise<boolean>;
  onDeleteCloudData: () => Promise<boolean>;
  onSetCustomUserId: (userId: string) => Promise<boolean>;
  onGetUserStats: () => Promise<any>;
}

export const CloudStorageManager: React.FC<CloudStorageManagerProps> = ({
  syncStatus,
  userIdentity,
  lastModified,
  onForceSyncToCloud,
  onForceLoadFromCloud,
  onCreateBackup,
  onRestoreFromBackup,
  onDeleteCloudData,
  onSetCustomUserId,
  onGetUserStats: _onGetUserStats,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [customUserId, setCustomUserId] = useState('');
  const [showUserIdInput, setShowUserIdInput] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleForceSyncToCloud = async () => {
    setIsLoading(true);
    try {
      const success = await onForceSyncToCloud();
      showMessage(success ? 'success' : 'error', success ? '同步到云端成功' : '同步到云端失败');
    } catch (error) {
      showMessage('error', '同步到云端失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceLoadFromCloud = async () => {
    setIsLoading(true);
    try {
      const success = await onForceLoadFromCloud();
      showMessage(success ? 'success' : 'error', success ? '从云端加载成功' : '从云端加载失败');
    } catch (error) {
      showMessage('error', '从云端加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBackup = async () => {
    setIsLoading(true);
    try {
      const backup = await onCreateBackup();
      if (backup) {
        // 下载备份文件
        const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `stock-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showMessage('success', '备份文件已下载');
      } else {
        showMessage('error', '创建备份失败');
      }
    } catch (error) {
      showMessage('error', '创建备份失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreFromBackup = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    try {
      const text = await file.text();
      const backup = JSON.parse(text) as UserDataBackup;
      
      const success = await onRestoreFromBackup(backup);
      showMessage(success ? 'success' : 'error', success ? '数据恢复成功' : '数据恢复失败');
    } catch (error) {
      showMessage('error', '备份文件格式错误');
    } finally {
      setIsLoading(false);
      // 重置文件输入
      event.target.value = '';
    }
  };

  const handleDeleteCloudData = async () => {
    if (!confirm('确定要删除云端数据吗？此操作不可恢复。')) {
      return;
    }

    setIsLoading(true);
    try {
      const success = await onDeleteCloudData();
      showMessage(success ? 'success' : 'error', success ? '云端数据已删除' : '删除云端数据失败');
    } catch (error) {
      showMessage('error', '删除云端数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetCustomUserId = async () => {
    if (!customUserId.trim()) {
      showMessage('error', '请输入有效的用户ID');
      return;
    }

    setIsLoading(true);
    try {
      const success = await onSetCustomUserId(customUserId.trim());
      if (success) {
        showMessage('success', '用户ID设置成功');
        setShowUserIdInput(false);
        setCustomUserId('');
      } else {
        showMessage('error', '用户ID设置失败');
      }
    } catch (error) {
      showMessage('error', '用户ID设置失败');
    } finally {
      setIsLoading(false);
    }
  };

  const getSyncStatusIcon = () => {
    if (!syncStatus.isOnline) {
      return <CloudOff className="w-4 h-4 text-gray-400" />;
    }
    
    if (syncStatus.syncInProgress) {
      return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
    }
    
    if (syncStatus.hasLocalChanges) {
      return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    }
    
    return <Cloud className="w-4 h-4 text-green-500" />;
  };

  const getSyncStatusText = () => {
    if (!syncStatus.isOnline) return '离线';
    if (syncStatus.syncInProgress) return '同步中...';
    if (syncStatus.hasLocalChanges) return '有本地更改';
    if (syncStatus.lastSyncTime) return '已同步';
    return '未同步';
  };

  return (
    <div className="bg-white rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getSyncStatusIcon()}
          <div>
            <h3 className="font-medium text-gray-900">云端存储</h3>
            <p className="text-sm text-gray-500">{getSyncStatusText()}</p>
          </div>
        </div>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-50"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>

      {message && (
        <div className={`mt-3 p-3 rounded-lg text-sm ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-700 border border-green-200' 
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            {message.type === 'success' ? (
              <CheckCircle className="w-4 h-4" />
            ) : (
              <AlertCircle className="w-4 h-4" />
            )}
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {isExpanded && (
        <div className="mt-4 space-y-4 border-t pt-4">
          {/* 用户信息 */}
          <div className="text-sm text-gray-600">
            <p><strong>用户ID:</strong> {userIdentity?.userId || '未设置'}</p>
            <p><strong>设备ID:</strong> {userIdentity?.deviceId || '未设置'}</p>
            <p><strong>最后修改:</strong> {new Date(lastModified).toLocaleString()}</p>
          </div>

          {/* 同步操作 */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={handleForceSyncToCloud}
              disabled={isLoading || !syncStatus.isOnline}
              className="flex items-center justify-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Upload className="w-4 h-4" />
              <span>上传到云端</span>
            </button>
            
            <button
              onClick={handleForceLoadFromCloud}
              disabled={isLoading || !syncStatus.isOnline}
              className="flex items-center justify-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download className="w-4 h-4" />
              <span>从云端下载</span>
            </button>
          </div>

          {/* 备份操作 */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={handleCreateBackup}
              disabled={isLoading || !syncStatus.isOnline}
              className="flex items-center justify-center space-x-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download className="w-4 h-4" />
              <span>导出备份</span>
            </button>
            
            <label className="flex items-center justify-center space-x-2 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 cursor-pointer">
              <Upload className="w-4 h-4" />
              <span>导入备份</span>
              <input
                type="file"
                accept=".json"
                onChange={handleRestoreFromBackup}
                className="hidden"
                disabled={isLoading}
              />
            </label>
          </div>

          {/* 用户ID设置 */}
          {showUserIdInput ? (
            <div className="flex space-x-2">
              <input
                type="text"
                value={customUserId}
                onChange={(e) => setCustomUserId(e.target.value)}
                placeholder="输入自定义用户ID"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleSetCustomUserId}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                设置
              </button>
              <button
                onClick={() => {
                  setShowUserIdInput(false);
                  setCustomUserId('');
                }}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              >
                取消
              </button>
            </div>
          ) : (
            <button
              onClick={() => setShowUserIdInput(true)}
              className="w-full px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              设置自定义用户ID
            </button>
          )}

          {/* 危险操作 */}
          <button
            onClick={handleDeleteCloudData}
            disabled={isLoading || !syncStatus.isOnline}
            className="flex items-center justify-center space-x-2 w-full px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 className="w-4 h-4" />
            <span>删除云端数据</span>
          </button>
        </div>
      )}
    </div>
  );
};
