# -*- coding: utf-8 -*-
# 股票资金流应用 - 快速部署配置脚本（中文版）
# 运行此脚本来配置Cloudflare部署

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 设置PowerShell会话编码
$PSDefaultParameterValues['*:Encoding'] = 'utf8'

Write-Host "股票资金流应用 - 部署配置向导" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# 检查必要工具
Write-Host ""
Write-Host "检查必要工具..." -ForegroundColor Yellow

# 检查Node.js
try {
    $nodeVersion = node --version
    Write-Host "[成功] Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm
try {
    $npmVersion = npm --version
    Write-Host "[成功] npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未找到npm" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "配置步骤指南:" -ForegroundColor Cyan
Write-Host "1. 创建Cloudflare KV命名空间"
Write-Host "2. 配置GitHub Secrets"
Write-Host "3. 更新配置文件"
Write-Host "4. 部署应用"

Write-Host ""
Write-Host "请按照以下步骤操作:" -ForegroundColor Yellow

Write-Host ""
Write-Host "第1步: 创建KV命名空间:" -ForegroundColor Cyan
Write-Host "   访问: https://dash.cloudflare.com"
Write-Host "   导航: Workers & Pages -> KV"
Write-Host "   创建以下命名空间:"
Write-Host "   - gupiao-stock-cache-prod"
Write-Host "   - gupiao-stock-config-prod"
Write-Host "   - gupiao-stock-cache-preview"
Write-Host "   - gupiao-stock-config-preview"

Write-Host ""
Write-Host "第2步: 获取Cloudflare信息:" -ForegroundColor Cyan
Write-Host "   API Token: https://dash.cloudflare.com/profile/api-tokens"
Write-Host "   Account ID: 在Dashboard右侧边栏"

Write-Host ""
Write-Host "第3步: 配置GitHub Secrets:" -ForegroundColor Cyan
$repoUrl = git config --get remote.origin.url
if ($repoUrl) {
    $repoUrl = $repoUrl -replace "\.git$", ""
    $repoUrl = $repoUrl -replace "git@github\.com:", "https://github.com/"
    Write-Host "   访问: $repoUrl/settings/secrets/actions"
} else {
    Write-Host "   访问: https://github.com/YOUR_USERNAME/gupiao_zijinliu/settings/secrets/actions"
}

Write-Host "   添加以下Secrets:"
Write-Host "   - CLOUDFLARE_API_TOKEN"
Write-Host "   - CLOUDFLARE_ACCOUNT_ID"
Write-Host "   - VITE_API_BASE_URL"
Write-Host "   - WORKERS_URL"
Write-Host "   - FRONTEND_URL"

Write-Host ""
Write-Host "第4步: 更新配置文件:" -ForegroundColor Cyan
Write-Host "   编辑 workers/wrangler.toml - 更新KV命名空间ID"
Write-Host "   编辑 .env.production - 更新API URL"

Write-Host ""
Write-Host "第5步: 部署应用:" -ForegroundColor Cyan
Write-Host "   git add ."
Write-Host "   git commit -m 'fix: 配置Workers部署和CORS设置'"
Write-Host "   git push origin main"

Write-Host ""
Write-Host "详细说明文档:" -ForegroundColor Green
Write-Host "   查看: docs/CORS_DEPLOYMENT_GUIDE.md"

Write-Host ""
Write-Host "部署完成后测试:" -ForegroundColor Magenta
Write-Host "   前端: https://sto-fund.pages.dev"
Write-Host "   API健康检查: https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev/health"
Write-Host "   API文档: https://gupiao-zijinliu-api-prod.YOUR_ACCOUNT.workers.dev/api/docs"

Write-Host ""
Write-Host "配置向导完成！请按照上述步骤操作。" -ForegroundColor Green

# 询问是否打开相关页面
Write-Host ""
Write-Host "是否打开Cloudflare Dashboard? (y/n): " -NoNewline -ForegroundColor Yellow
$openPages = Read-Host
if ($openPages -eq "y" -or $openPages -eq "Y") {
    Start-Process "https://dash.cloudflare.com"
}

Write-Host "是否打开部署指南文档? (y/n): " -NoNewline -ForegroundColor Yellow
$openDocs = Read-Host
if ($openDocs -eq "y" -or $openDocs -eq "Y") {
    Start-Process "docs/CORS_DEPLOYMENT_GUIDE.md"
}

Write-Host ""
Write-Host "提醒: 完成配置后，推送代码到main分支即可自动部署！" -ForegroundColor Yellow
