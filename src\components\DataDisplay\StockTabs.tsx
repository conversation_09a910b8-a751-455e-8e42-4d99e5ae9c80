import React, { useState, useRef, useEffect } from 'react';
import { useStockList } from '@/hooks/useStockData';
import { X, ChevronLeft, ChevronRight, Plus } from 'lucide-react';

interface StockTabsProps {
  /** 选中的股票代码 */
  selectedStock: string | null;
  /** 股票选择事件 */
  onStockSelect: (code: string) => void;
  /** 最大显示标签数 */
  maxTabs?: number;
  /** 自定义类名 */
  className?: string;
}

/**
 * 股票切换标签组件
 */
export const StockTabs: React.FC<StockTabsProps> = ({
  selectedStock,
  onStockSelect,
  maxTabs = 8,
  className = '',
}) => {
  const { stocks, removeStock } = useStockList();
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 检查滚动状态
  const checkScrollState = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollLeft, scrollWidth, clientWidth } = container;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    setShowScrollButtons(scrollWidth > clientWidth);
  };

  // 滚动到指定位置
  const scrollTo = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const scrollAmount = 200;
    const newScrollLeft = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  };

  // 滚动到选中的标签
  const scrollToSelected = () => {
    if (!selectedStock) return;
    
    const container = scrollContainerRef.current;
    const selectedTab = container?.querySelector(`[data-stock-code="${selectedStock}"]`) as HTMLElement;
    
    if (container && selectedTab) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = selectedTab.getBoundingClientRect();
      
      if (tabRect.left < containerRect.left || tabRect.right > containerRect.right) {
        selectedTab.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  };

  // 监听容器尺寸变化
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(checkScrollState);
    resizeObserver.observe(container);

    container.addEventListener('scroll', checkScrollState);
    checkScrollState();

    return () => {
      resizeObserver.disconnect();
      container.removeEventListener('scroll', checkScrollState);
    };
  }, [stocks]);

  // 选中股票变化时滚动到对应标签
  useEffect(() => {
    scrollToSelected();
  }, [selectedStock]);

  // 处理标签关闭
  const handleCloseTab = async (code: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    try {
      await removeStock(code);
      
      // 如果关闭的是当前选中的标签，选择下一个或上一个
      if (selectedStock === code) {
        const currentIndex = stocks.findIndex(s => s.code === code);
        const nextStock = stocks[currentIndex + 1] || stocks[currentIndex - 1];
        
        if (nextStock) {
          onStockSelect(nextStock.code);
        } else {
          onStockSelect('');
        }
      }
    } catch (error) {
      console.error('删除股票失败:', error);
    }
  };

  // 显示的股票列表（限制数量）
  const displayStocks = stocks.slice(0, maxTabs);
  const hasMoreStocks = stocks.length > maxTabs;

  if (stocks.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="flex items-center">
        {/* 左滚动按钮 */}
        {showScrollButtons && (
          <button
            onClick={() => scrollTo('left')}
            disabled={!canScrollLeft}
            className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed"
            aria-label="向左滚动"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
        )}

        {/* 标签容器 */}
        <div 
          ref={scrollContainerRef}
          className="flex-1 flex overflow-x-auto scrollbar-hide"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          <div className="flex">
            {displayStocks.map((stock) => {
              const isSelected = selectedStock === stock.code;
              
              return (
                <button
                  key={stock.code}
                  data-stock-code={stock.code}
                  onClick={() => onStockSelect(stock.code)}
                  className={`
                    flex-shrink-0 group relative px-4 py-3 text-sm font-medium border-b-2 transition-colors
                    ${isSelected
                      ? 'border-primary-600 text-primary-600 bg-primary-50'
                      : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center gap-2 max-w-32">
                    <div className="truncate">
                      <div className="text-xs text-gray-500 truncate">
                        {stock.name}
                      </div>
                      <div className="font-mono text-xs">
                        {stock.code}
                      </div>
                    </div>
                    
                    {/* 关闭按钮 */}
                    <button
                      onClick={(e) => handleCloseTab(stock.code, e)}
                      className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all"
                      aria-label={`移除 ${stock.name}`}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                </button>
              );
            })}

            {/* 更多股票指示器 */}
            {hasMoreStocks && (
              <div className="flex-shrink-0 px-3 py-3 text-xs text-gray-500 border-b-2 border-transparent">
                <div className="flex items-center gap-1">
                  <Plus className="w-3 h-3" />
                  <span>{stocks.length - maxTabs} 更多</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右滚动按钮 */}
        {showScrollButtons && (
          <button
            onClick={() => scrollTo('right')}
            disabled={!canScrollRight}
            className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed"
            aria-label="向右滚动"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};
