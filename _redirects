# Cloudflare Pages Redirects Configuration

# SPA Fallback - All routes should serve index.html for client-side routing
/*    /index.html   200

# API Proxy to Workers (if needed for development)
# /api/*  https://gupiao-zijinliu-api.your-subdomain.workers.dev/api/:splat  200

# Redirect old URLs (if any)
# /old-path  /new-path  301

# Force HTTPS (handled by Cloudflare by default, but can be explicit)
# http://example.com/*  https://example.com/:splat  301
