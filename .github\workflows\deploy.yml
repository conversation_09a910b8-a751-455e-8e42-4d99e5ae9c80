name: Deploy to Cloudflare

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18'

jobs:
  # 前端构建和部署
  deploy-frontend:
    name: Deploy Frontend to Cloudflare Pages
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npm run type-check

      - name: Build frontend
        run: npm run build
        env:
          VITE_API_BASE_URL: https://gupiao-zijinliu-api-prod.yiukaitlina.workers.dev
          VITE_APP_TITLE: 股票资金流向监控
          VITE_REFRESH_INTERVAL: 60000
          VITE_ENABLE_DEVTOOLS: false

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: gupiao-zijinliu
          directory: dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          wranglerVersion: '3'

  # Workers构建和部署
  deploy-workers:
    name: Deploy Workers API
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./workers
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: workers/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npx tsc --noEmit

      - name: Deploy to Cloudflare Workers (Development)
        if: github.ref == 'refs/heads/develop'
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: workers
          command: deploy --env development
          secrets: |
            ENVIRONMENT
            API_BASE_URL
            CRON_ENABLED
            CACHE_TTL
            BATCH_SIZE
            MAX_RETRIES
            LOG_LEVEL
        env:
          ENVIRONMENT: development
          API_BASE_URL: https://push2.eastmoney.com
          CRON_ENABLED: true
          CACHE_TTL: 60
          BATCH_SIZE: 5
          MAX_RETRIES: 3
          LOG_LEVEL: debug

      - name: Deploy to Cloudflare Workers (Production)
        if: github.ref == 'refs/heads/main'
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: workers
          command: deploy --env production
          secrets: |
            ENVIRONMENT
            API_BASE_URL
            CRON_ENABLED
            CACHE_TTL
            BATCH_SIZE
            MAX_RETRIES
            LOG_LEVEL
        env:
          ENVIRONMENT: production
          API_BASE_URL: https://push2.eastmoney.com
          CRON_ENABLED: true
          CACHE_TTL: 60
          BATCH_SIZE: 10
          MAX_RETRIES: 3
          LOG_LEVEL: info

  # 健康检查
  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-workers]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Wait for deployment
        run: sleep 30

      - name: Check Workers API
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ secrets.WORKERS_URL }}/health)
          if [ $response -eq 200 ]; then
            echo "✅ Workers API is healthy"
          else
            echo "❌ Workers API health check failed (HTTP $response)"
            exit 1
          fi

      - name: Check Frontend
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ secrets.FRONTEND_URL }})
          if [ $response -eq 200 ]; then
            echo "✅ Frontend is accessible"
          else
            echo "❌ Frontend health check failed (HTTP $response)"
            exit 1
          fi

      - name: API Functionality Test
        run: |
          # 测试API基本功能
          api_response=$(curl -s ${{ secrets.WORKERS_URL }}/api/test)
          if echo "$api_response" | grep -q "API测试完成"; then
            echo "✅ API functionality test passed"
          else
            echo "❌ API functionality test failed"
            echo "Response: $api_response"
            exit 1
          fi
