import { useState, useCallback, useEffect } from 'react';
import { Stock } from '@/types';
import { validateStockCode, formatStockCode, isDuplicateCode, parseCSVContent, StockInfo as ParsedStockInfo } from '@/utils/validation';
import { useCloudStorage } from './useCloudStorage';
import { StockInfo } from '@/types/stock';

// 常见股票名称映射
const STOCK_NAME_MAP: Record<string, string> = {
  '000001': '平安银行',
  '000002': '万科A',
  '000858': '五粮液',
  '600036': '招商银行',
  '600121': '郑州煤电',
  '600519': '贵州茅台',
  '002415': '海康威视',
  '600276': '恒瑞医药',
  '000725': '京东方A',
  '600793': '宜宾纸业',
  '603067': '振华股份',
  // 可以继续添加更多股票
};

/**
 * 获取股票名称
 * @param code 股票代码
 * @returns 股票名称
 */
function getStockName(code: string): string {
  return STOCK_NAME_MAP[code] || `股票${code}`;
}

interface UseStockListReturn {
  stocks: Stock[];
  addStock: (code: string, name?: string) => Promise<{ success: boolean; message?: string }>;
  batchAddStocks: (stockInfos: ParsedStockInfo[]) => Promise<{
    success: boolean;
    message: string;
    successCount: number;
    failedCount: number;
    duplicateCount: number;
    errors: string[];
  }>;
  importFromCSV: (csvContent: string) => Promise<{
    success: boolean;
    message: string;
    successCount: number;
    failedCount: number;
    duplicateCount: number;
    parseErrors: string[];
    addErrors: string[];
    totalRows: number;
  }>;
  removeStock: (code: string) => void;
  batchRemoveStocks: (codes: string[]) => void;
  clearAllStocks: () => void;
  reorderStocks: (activeId: string, overId: string) => void;
  isLoading: boolean;
  error: string | null;
  // 云端存储相关
  syncStatus: any;
  userIdentity: any;
  lastModified: string;
  forceSyncToCloud: () => Promise<boolean>;
  forceLoadFromCloud: () => Promise<boolean>;
  createBackup: () => Promise<any>;
  restoreFromBackup: (backup: any) => Promise<boolean>;
  deleteCloudData: () => Promise<boolean>;
  setCustomUserId: (userId: string) => Promise<boolean>;
  getUserStats: () => Promise<any>;
}

const STORAGE_KEY = 'gupiao-stock-list';

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListReturn {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastModified, setLastModified] = useState<string>(new Date().toISOString());

  // 云端存储管理
  const cloudStorage = useCloudStorage();

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // 首先从 localStorage 加载数据
        const savedStocks = localStorage.getItem(STORAGE_KEY);
        const savedLastModified = localStorage.getItem(`${STORAGE_KEY}_lastModified`);

        let localStocks: Stock[] = [];
        let localLastModified = new Date().toISOString();

        if (savedStocks) {
          localStocks = JSON.parse(savedStocks);
          // 确保股票有 sortOrder，如果没有则按索引分配
          localStocks = localStocks.map((stock, index) => ({
            ...stock,
            sortOrder: stock.sortOrder ?? index
          }));
          // 按 sortOrder 排序
          localStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
          localLastModified = savedLastModified || localLastModified;
        }

        // 设置本地数据
        setStocks(localStocks);
        setLastModified(localLastModified);

        // 如果在线，尝试从云端同步数据
        if (cloudStorage.syncStatus.isOnline && cloudStorage.userIdentity) {
          try {
            const cloudData = await cloudStorage.getFromCloud();

            if (cloudData && cloudData.stocks.length > 0) {
              // 比较时间戳，决定使用哪个数据
              const cloudTime = new Date(cloudData.lastModified).getTime();
              const localTime = new Date(localLastModified).getTime();

              if (cloudTime > localTime) {
                // 云端数据更新，使用云端数据
                const convertedStocks = cloudData.stocks.map((stock, index) => ({
                  code: stock.code,
                  name: stock.name,
                  sortOrder: stock.sortOrder ?? index,
                }));
                // 按 sortOrder 排序
                convertedStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

                setStocks(convertedStocks);
                setLastModified(cloudData.lastModified);
                saveToStorage(convertedStocks, cloudData.lastModified);
              } else if (localTime > cloudTime && localStocks.length > 0) {
                // 本地数据更新，同步到云端
                const stockInfos: StockInfo[] = localStocks.map(stock => ({
                  code: stock.code,
                  name: stock.name,
                  addedAt: new Date().toISOString(),
                  sortOrder: stock.sortOrder,
                }));

                await cloudStorage.syncToCloud(stockInfos, localLastModified);
              }
            } else if (localStocks.length > 0) {
              // 云端无数据，上传本地数据
              const stockInfos: StockInfo[] = localStocks.map(stock => ({
                code: stock.code,
                name: stock.name,
                addedAt: new Date().toISOString(),
                sortOrder: stock.sortOrder,
              }));

              await cloudStorage.syncToCloud(stockInfos, localLastModified);
            }
          } catch (cloudError) {
            console.warn('云端数据同步失败，使用本地数据:', cloudError);
          }
        }
      } catch (err) {
        console.error('初始化数据失败:', err);
        setError('初始化数据失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (cloudStorage.userIdentity) {
      initializeData();
    }
  }, [cloudStorage.userIdentity, cloudStorage.syncStatus.isOnline]);

  // 保存股票列表到localStorage和云端
  const saveToStorage = useCallback((stockList: Stock[], timestamp?: string) => {
    const saveTime = timestamp || new Date().toISOString();

    try {
      // 保存到 localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stockList));
      localStorage.setItem(`${STORAGE_KEY}_lastModified`, saveTime);

      setLastModified(saveTime);

      // 标记本地数据有变更，触发云端同步
      if (cloudStorage.syncStatus.isOnline) {
        cloudStorage.markLocalChanges();

        // 异步同步到云端
        const stockInfos: StockInfo[] = stockList.map(stock => ({
          code: stock.code,
          name: stock.name,
          addedAt: new Date().toISOString(),
          sortOrder: stock.sortOrder,
        }));

        cloudStorage.syncToCloud(stockInfos, saveTime).catch(error => {
          console.warn('云端同步失败:', error);
        });
      }
    } catch (err) {
      console.error('保存股票列表失败:', err);
      setError('保存股票列表失败');
    }
  }, [cloudStorage]);

  // 添加股票
  const addStock = useCallback(async (code: string, name?: string): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证股票代码
      const validation = validateStockCode(code);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const formattedCode = formatStockCode(code);
      const existingCodes = stocks.map(stock => stock.code);

      // 检查重复
      if (isDuplicateCode(formattedCode, existingCodes)) {
        return { success: false, message: '股票代码已存在' };
      }

      // 使用传入的名称，如果没有则使用映射表或默认格式
      const stockName = name || getStockName(formattedCode);

      // 创建新股票对象，分配最大的 sortOrder + 1
      const maxSortOrder = stocks.length > 0 ? Math.max(...stocks.map(s => s.sortOrder || 0)) : -1;
      const newStock: Stock = {
        code: formattedCode,
        name: stockName,
        sortOrder: maxSortOrder + 1,
      };

      // 更新股票列表
      const updatedStocks = [...stocks, newStock];
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票添加成功' };
    } catch (err) {
      const errorMessage = '添加股票失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 删除股票
  const removeStock = useCallback((code: string) => {
    setError(null);

    try {
      const updatedStocks = stocks.filter(stock => stock.code !== code);
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('删除股票失败:', err);
      setError('删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 批量删除股票
  const batchRemoveStocks = useCallback((codes: string[]) => {
    setError(null);

    try {
      if (codes.length === 0) {
        return;
      }

      const updatedStocks = stocks.filter(stock => !codes.includes(stock.code));
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('批量删除股票失败:', err);
      setError('批量删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 清空所有股票
  const clearAllStocks = useCallback(() => {
    setError(null);

    try {
      setStocks([]);
      saveToStorage([]);
    } catch (err) {
      console.error('清空股票列表失败:', err);
      setError('清空股票列表失败');
    }
  }, [saveToStorage]);

  // 重新排序股票
  const reorderStocks = useCallback((activeId: string, overId: string) => {
    setError(null);

    try {
      const activeIndex = stocks.findIndex(stock => stock.code === activeId);
      const overIndex = stocks.findIndex(stock => stock.code === overId);

      if (activeIndex === -1 || overIndex === -1) {
        return;
      }

      // 创建新的股票数组
      const newStocks = [...stocks];
      const [movedStock] = newStocks.splice(activeIndex, 1);
      newStocks.splice(overIndex, 0, movedStock);

      // 重新分配 sortOrder
      const updatedStocks = newStocks.map((stock, index) => ({
        ...stock,
        sortOrder: index,
      }));

      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('重新排序失败:', err);
      setError('重新排序失败');
    }
  }, [stocks, saveToStorage]);

  // 强制同步到云端
  const forceSyncToCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const stockInfos: StockInfo[] = stocks.map(stock => ({
        code: stock.code,
        name: stock.name,
        addedAt: new Date().toISOString(),
        sortOrder: stock.sortOrder,
      }));

      const result = await cloudStorage.syncToCloud(stockInfos, lastModified, true);
      return result !== null;
    } catch (error) {
      console.error('强制同步失败:', error);
      setError('强制同步失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [stocks, lastModified, cloudStorage]);

  // 从云端强制拉取数据
  const forceLoadFromCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const cloudData = await cloudStorage.getFromCloud();

      if (cloudData) {
        const convertedStocks = cloudData.stocks.map((stock, index) => ({
          code: stock.code,
          name: stock.name,
          sortOrder: stock.sortOrder ?? index,
        }));
        // 按 sortOrder 排序
        convertedStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

        setStocks(convertedStocks);
        setLastModified(cloudData.lastModified);
        saveToStorage(convertedStocks, cloudData.lastModified);
        return true;
      }

      return false;
    } catch (error) {
      console.error('从云端加载失败:', error);
      setError('从云端加载失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudStorage, saveToStorage]);

  // 批量添加股票
  const batchAddStocks = useCallback(async (stockInfos: ParsedStockInfo[]): Promise<{
    success: boolean;
    message: string;
    successCount: number;
    failedCount: number;
    duplicateCount: number;
    errors: string[];
  }> => {
    if (stockInfos.length === 0) {
      return {
        success: false,
        message: '没有有效的股票信息',
        successCount: 0,
        failedCount: 0,
        duplicateCount: 0,
        errors: ['股票信息列表为空']
      };
    }

    setIsLoading(true);
    setError(null);

    const errors: string[] = [];
    const successStocks: Stock[] = [];
    const duplicateCodes: string[] = [];
    const existingCodes = stocks.map(stock => stock.code);

    try {
      for (const stockInfo of stockInfos) {
        // 验证股票代码
        const validation = validateStockCode(stockInfo.code);
        if (!validation.isValid) {
          errors.push(`${stockInfo.code}: ${validation.message}`);
          continue;
        }

        const formattedCode = formatStockCode(stockInfo.code);

        // 检查重复
        if (isDuplicateCode(formattedCode, existingCodes) ||
            successStocks.some(s => s.code === formattedCode)) {
          duplicateCodes.push(formattedCode);
          continue;
        }

        // 使用CSV中的名称，如果没有则使用映射表或默认格式
        const stockName = stockInfo.name || getStockName(formattedCode);

        successStocks.push({
          code: formattedCode,
          name: stockName,
          sortOrder: 0 // 临时值，稍后会重新分配
        });
      }

      // 批量创建股票对象
      if (successStocks.length > 0) {
        const maxSortOrder = stocks.length > 0 ? Math.max(...stocks.map(s => s.sortOrder || 0)) : -1;

        // 重新分配sortOrder
        const newStocks: Stock[] = successStocks.map((stock, index) => ({
          ...stock,
          sortOrder: maxSortOrder + index + 1,
        }));

        // 更新股票列表
        const updatedStocks = [...stocks, ...newStocks];
        setStocks(updatedStocks);
        saveToStorage(updatedStocks);
      }

      const successCount = successStocks.length;
      const failedCount = errors.length;
      const duplicateCount = duplicateCodes.length;

      let message = '';
      if (successCount > 0) {
        message += `成功添加 ${successCount} 只股票`;
      }
      if (duplicateCount > 0) {
        message += (message ? '，' : '') + `跳过 ${duplicateCount} 只重复股票`;
      }
      if (failedCount > 0) {
        message += (message ? '，' : '') + `${failedCount} 只股票添加失败`;
      }

      return {
        success: successCount > 0,
        message: message || '没有股票被添加',
        successCount,
        failedCount,
        duplicateCount,
        errors
      };

    } catch (err) {
      const errorMessage = '批量添加股票失败';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage,
        successCount: 0,
        failedCount: stockInfos.length,
        duplicateCount: 0,
        errors: [errorMessage]
      };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 从CSV文件批量导入股票
  const importFromCSV = useCallback(async (csvContent: string): Promise<{
    success: boolean;
    message: string;
    successCount: number;
    failedCount: number;
    duplicateCount: number;
    parseErrors: string[];
    addErrors: string[];
    totalRows: number;
  }> => {
    // 解析CSV内容
    const parseResult = parseCSVContent(csvContent);

    if (!parseResult.success) {
      return {
        success: false,
        message: 'CSV文件解析失败',
        successCount: 0,
        failedCount: 0,
        duplicateCount: 0,
        parseErrors: parseResult.errors,
        addErrors: [],
        totalRows: parseResult.totalRows
      };
    }

    // 批量添加股票
    const addResult = await batchAddStocks(parseResult.stockInfos);

    return {
      success: addResult.success,
      message: addResult.message,
      successCount: addResult.successCount,
      failedCount: addResult.failedCount,
      duplicateCount: addResult.duplicateCount,
      parseErrors: parseResult.errors,
      addErrors: addResult.errors,
      totalRows: parseResult.totalRows
    };
  }, [batchAddStocks]);

  return {
    stocks,
    addStock,
    batchAddStocks,
    importFromCSV,
    removeStock,
    batchRemoveStocks,
    clearAllStocks,
    reorderStocks,
    isLoading,
    error,
    // 云端存储相关
    syncStatus: cloudStorage.syncStatus,
    userIdentity: cloudStorage.userIdentity,
    lastModified,
    forceSyncToCloud,
    forceLoadFromCloud,
    createBackup: cloudStorage.createBackup,
    restoreFromBackup: cloudStorage.restoreFromBackup,
    deleteCloudData: cloudStorage.deleteCloudData,
    setCustomUserId: cloudStorage.setCustomUserId,
    getUserStats: cloudStorage.getUserStats,
  };
}
