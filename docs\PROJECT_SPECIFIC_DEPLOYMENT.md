# 股票资金流项目 - Cloudflare Pages 部署配置

## 📋 项目概述

**项目名称：** gupiao-zijinliu  
**技术栈：** React + TypeScript + Vite + TailwindCSS + ECharts  
**部署目标：** Cloudflare Pages  

---

## 🔧 当前项目配置分析

### 项目结构
```
gupiao-zijinliu/
├── src/                    # React 源代码
├── workers/               # Cloudflare Workers API
├── dist/                  # 构建输出目录
├── package.json           # ✅ 已配置构建脚本
├── vite.config.ts         # ✅ 已优化构建配置
├── _headers              # ✅ 已配置安全头
├── _redirects            # ✅ 已配置 SPA 路由
└── tailwind.config.js    # ✅ TailwindCSS 配置
```

### 现有构建配置（已优化）
```json
{
  "scripts": {
    "build": "tsc && vite build",
    "build:prod": "tsc && vite build --mode production"
  }
}
```

---

## 🚀 Cloudflare Pages 部署步骤

### 1. 基本配置
```
Project name: gupiao-zijinliu
Production branch: main
Framework preset: Vite
Build command: npm run build:prod
Build output directory: dist
Root directory: / (留空)
```

### 2. 环境变量配置

**生产环境：**
```
NODE_VERSION=18
VITE_APP_ENV=production
VITE_API_URL=https://gupiao-zijinliu-api.your-subdomain.workers.dev
```

**预览环境：**
```
NODE_VERSION=18
VITE_APP_ENV=staging
VITE_API_URL=https://gupiao-zijinliu-api-staging.your-subdomain.workers.dev
```

### 3. Workers 集成配置

由于项目包含 `workers/` 目录，建议同时部署 API：

**workers/wrangler.toml 配置：**
```toml
name = "gupiao-zijinliu-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"

[env.production]
name = "gupiao-zijinliu-api"

[env.staging]
name = "gupiao-zijinliu-api-staging"
```

---

## 📁 项目特定文件配置

### 1. 更新 _headers（已存在，建议增强）
```
/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # CSP for ECharts
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://push2.eastmoney.com https://*.workers.dev;
  
  # CORS for API
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
  
  # Cache Control
  Cache-Control: public, max-age=3600

# Static Assets Caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# API Proxy
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# ECharts Assets
/*.js
  Cache-Control: public, max-age=86400

/*.css
  Cache-Control: public, max-age=86400
```

### 2. 更新 _redirects（已存在，建议增强）
```
# SPA Fallback
/*    /index.html   200

# API Proxy to Workers
/api/*  https://gupiao-zijinliu-api.your-subdomain.workers.dev/api/:splat  200

# Health Check
/health  https://gupiao-zijinliu-api.your-subdomain.workers.dev/health  200
```

### 3. 增强 vite.config.ts（当前配置已很好）
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['echarts', 'echarts-for-react'],
          ui: ['lucide-react'],
          query: ['react-query'],
        },
      },
    },
    assetsInlineLimit: 4096,
    sourcemap: false,
  },
  // 生产环境 API 代理配置
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
})
```

---

## 🔄 部署工作流

### 自动部署流程
```bash
# 1. 开发完成后推送代码
git add .
git commit -m "feat: 添加新功能"
git push origin main

# 2. Cloudflare Pages 自动触发构建
# 3. 构建成功后自动部署到全球 CDN
# 4. 获得部署 URL: https://gupiao-zijinliu.pages.dev
```

### 手动部署（使用 Wrangler）
```bash
# 安装 Wrangler CLI
npm install -g wrangler

# 构建项目
npm run build:prod

# 部署到 Cloudflare Pages
wrangler pages publish dist --project-name gupiao-zijinliu

# 部署 Workers API
cd workers
wrangler deploy
```

---

## 🎯 性能优化建议

### 1. ECharts 优化
```typescript
// 按需引入 ECharts 组件
import * as echarts from 'echarts/core'
import { LineChart, BarChart } from 'echarts/charts'
import { GridComponent, TooltipComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([LineChart, BarChart, GridComponent, TooltipComponent, CanvasRenderer])
```

### 2. 代码分割优化
```typescript
// 路由懒加载
const StockAnalysis = lazy(() => import('@/components/StockAnalysis'))
const Dashboard = lazy(() => import('@/components/Dashboard'))
```

### 3. 图片优化
```bash
# 添加图片压缩插件
npm install vite-plugin-imagemin --save-dev
```

---

## 🔍 监控和调试

### 1. 部署状态检查
```bash
# 检查部署历史
wrangler pages deployment list --project-name gupiao-zijinliu

# 查看构建日志
# Cloudflare Dashboard → Pages → gupiao-zijinliu → Deployments
```

### 2. 性能监控
```typescript
// 添加 Web Vitals 监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // 发送到 Cloudflare Analytics 或其他服务
  console.log(metric)
}

// 监控核心性能指标
getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

### 3. 错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 发送错误报告
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  // 发送错误报告
})
```

---

## 📋 部署检查清单

### 部署前检查
- [ ] `npm run build:prod` 构建成功
- [ ] ECharts 图表在生产构建中正常显示
- [ ] API 接口配置正确
- [ ] 环境变量已设置
- [ ] _headers 和 _redirects 文件配置正确

### 部署后验证
- [ ] 网站首页正常加载
- [ ] 股票数据图表正常显示
- [ ] API 请求成功（检查网络面板）
- [ ] 移动端响应式布局正常
- [ ] 页面路由切换正常

### 性能检查
- [ ] Lighthouse 性能分数 > 85
- [ ] 首屏加载时间 < 3 秒
- [ ] ECharts 图表渲染流畅
- [ ] 静态资源缓存生效

---

**🎉 您的股票资金流项目已准备好部署到 Cloudflare Pages！**

按照以上配置，您将获得一个高性能、全球分发的股票数据可视化应用。
