// 云端存储功能测试脚本
// 在浏览器控制台中运行此脚本来测试云端存储功能

const API_BASE_URL = 'https://gupiao-zijinliu-api-prod.yiukaitlina.workers.dev';

async function testCloudStorage() {
  console.log('🚀 开始测试云端存储功能...');
  
  try {
    // 1. 测试生成设备ID
    console.log('\n📱 测试生成设备ID...');
    const deviceResponse = await fetch(`${API_BASE_URL}/api/user/device-id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        additionalInfo: {
          test: true,
          timestamp: new Date().toISOString()
        }
      })
    });
    
    const deviceData = await deviceResponse.json();
    console.log('✅ 设备ID生成成功:', deviceData);
    
    const deviceId = deviceData.data.deviceId;
    const userId = deviceId; // 使用设备ID作为用户ID
    
    // 2. 测试同步空数据（首次同步）
    console.log('\n☁️ 测试首次同步（空数据）...');
    const syncRequest = {
      userIdentity: {
        deviceId: deviceId,
        userId: userId,
        sessionId: `session_${Date.now()}`
      },
      localStocks: [],
      localLastModified: new Date().toISOString(),
      forceOverwrite: false
    };
    
    const syncResponse = await fetch(`${API_BASE_URL}/api/user/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(syncRequest)
    });
    
    const syncData = await syncResponse.json();
    console.log('✅ 首次同步成功:', syncData);
    
    // 3. 测试添加股票数据并同步
    console.log('\n📈 测试添加股票数据并同步...');
    const testStocks = [
      { code: '600121', name: '郑州煤电', addedAt: new Date().toISOString() },
      { code: '000001', name: '平安银行', addedAt: new Date().toISOString() }
    ];
    
    const syncWithDataRequest = {
      userIdentity: {
        deviceId: deviceId,
        userId: userId,
        sessionId: `session_${Date.now()}`
      },
      localStocks: testStocks,
      localLastModified: new Date().toISOString(),
      forceOverwrite: false
    };
    
    const syncWithDataResponse = await fetch(`${API_BASE_URL}/api/user/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(syncWithDataRequest)
    });
    
    const syncWithDataResult = await syncWithDataResponse.json();
    console.log('✅ 股票数据同步成功:', syncWithDataResult);
    
    // 4. 测试获取用户股票数据
    console.log('\n📊 测试获取用户股票数据...');
    const getUserStocksResponse = await fetch(`${API_BASE_URL}/api/user/${userId}/stocks`);
    const userStocksData = await getUserStocksResponse.json();
    console.log('✅ 获取用户股票数据成功:', userStocksData);
    
    // 5. 测试获取用户统计信息
    console.log('\n📈 测试获取用户统计信息...');
    const getUserStatsResponse = await fetch(`${API_BASE_URL}/api/user/${userId}/stats`);
    const userStatsData = await getUserStatsResponse.json();
    console.log('✅ 获取用户统计信息成功:', userStatsData);
    
    // 6. 测试创建备份
    console.log('\n💾 测试创建数据备份...');
    const backupResponse = await fetch(`${API_BASE_URL}/api/user/${userId}/backup`);
    const backupData = await backupResponse.json();
    console.log('✅ 创建备份成功:', backupData);
    
    // 7. 测试从备份恢复（使用刚创建的备份）
    console.log('\n🔄 测试从备份恢复数据...');
    const restoreResponse = await fetch(`${API_BASE_URL}/api/user/restore`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(backupData.data)
    });
    
    const restoreResult = await restoreResponse.json();
    console.log('✅ 从备份恢复成功:', restoreResult);
    
    // 8. 测试冲突解决（模拟不同设备的数据冲突）
    console.log('\n⚡ 测试数据冲突解决...');
    const conflictStocks = [
      { code: '600519', name: '贵州茅台', addedAt: new Date().toISOString() },
      { code: '000858', name: '五粮液', addedAt: new Date().toISOString() }
    ];
    
    // 使用较早的时间戳模拟冲突
    const oldTimestamp = new Date(Date.now() - 60000).toISOString();
    
    const conflictSyncRequest = {
      userIdentity: {
        deviceId: `${deviceId}_conflict`,
        userId: userId,
        sessionId: `session_${Date.now()}`
      },
      localStocks: conflictStocks,
      localLastModified: oldTimestamp,
      forceOverwrite: false
    };
    
    const conflictSyncResponse = await fetch(`${API_BASE_URL}/api/user/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(conflictSyncRequest)
    });
    
    const conflictSyncResult = await conflictSyncResponse.json();
    console.log('✅ 冲突解决测试成功:', conflictSyncResult);
    
    console.log('\n🎉 所有云端存储功能测试完成！');
    console.log('\n📋 测试总结:');
    console.log('- ✅ 设备ID生成');
    console.log('- ✅ 数据同步（上传/下载）');
    console.log('- ✅ 用户数据获取');
    console.log('- ✅ 统计信息获取');
    console.log('- ✅ 数据备份创建');
    console.log('- ✅ 数据恢复');
    console.log('- ✅ 冲突解决');
    
    return {
      success: true,
      deviceId,
      userId,
      testResults: {
        deviceGeneration: deviceData,
        firstSync: syncData,
        dataSync: syncWithDataResult,
        userStocks: userStocksData,
        userStats: userStatsData,
        backup: backupData,
        restore: restoreResult,
        conflictResolution: conflictSyncResult
      }
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
console.log('在浏览器控制台中运行: testCloudStorage()');

// 如果在 Node.js 环境中，可以直接运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testCloudStorage };
}
