# 股票资金流向实时监控功能技术文档

## 项目概述

本项目实现了股票资金流向的实时监控功能，通过调用东方财富API获取股票资金流向数据，支持单股票和批量股票数据获取，具备完善的错误处理、重试机制、频率限制和缓存策略。

## 1. API调用清单

### 1.1 主要API接口

#### 资金流向数据API
- **URL**: `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get`
- **用途**: 获取股票资金流向K线数据
- **返回数据**: 包含主力资金、超大单、大单、中单、小单的流入流出数据
- **请求参数**:
  - `secid`: 股票标识符（格式：市场代码.股票代码）
  - `klt`: K线类型（1=分钟线）
  - `lmt`: 数据条数限制（默认240条）
  - `fields1`: 基础字段（f1,f2,f3,f7）
  - `fields2`: 详细字段（f51-f63，包含各类资金流向数据）

#### 实时行情数据API
- **URL**: `https://push2.eastmoney.com/api/qt/stock/get`
- **用途**: 获取股票实时行情数据（价格、涨跌幅等）
- **返回数据**: 股票当前价格、涨跌幅、成交量、成交额等
- **请求参数**:
  - `secid`: 股票标识符
  - `fields`: 字段列表（包含价格、涨跌幅等字段）

### 1.2 请求头配置

所有API请求使用统一的请求头：
```javascript
{
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  'Accept': '*/*',
  'Referer': 'https://data.eastmoney.com/zjlx/{股票代码}.html',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache'
}
```

## 2. 调用策略分析

### 2.1 频率限制机制

#### RateLimiter类实现
- **限制规则**: 每分钟最多60个请求（60 requests/60000ms）
- **实现方式**: 滑动窗口算法
- **核心逻辑**:
  ```typescript
  class RateLimiter {
    private requests: number[] = [];
    private readonly maxRequests: number = 60;
    private readonly windowMs: number = 60000;
    
    async waitForSlot(): Promise<void> {
      while (!this.canMakeRequest()) {
        await delay(100); // 等待100ms后重新检查
      }
      this.recordRequest();
    }
  }
  ```

### 2.2 批量调用策略

#### 串行处理机制
- **处理方式**: 串行处理，避免并发请求过多
- **延迟设置**: 每个股票请求间隔200ms
- **批量大小**: 前端限制每批最多20个股票，并发限制10个
- **实现代码**:
  ```typescript
  // 串行处理每个股票
  for (const code of codes) {
    const result = await this.apiService.getStockFlowData(code, limit);
    // 处理结果...
    
    // 添加延迟避免频率限制
    if (codes.indexOf(code) < codes.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
  ```

### 2.3 重试机制

#### 多层重试策略
1. **API服务层重试**:
   - 最大重试次数: 3次
   - 基础延迟: 1000ms
   - 重试条件: HTTP 5xx错误或429错误
   - 延迟策略: 指数退避

2. **前端查询层重试**:
   - React Query重试次数: 2次
   - 重试延迟: 指数退避（最大30秒）
   - 不重试条件: 4xx客户端错误

#### 重试实现代码
```typescript
const response = await fetchWithRetry(url, {
  method: 'GET',
  headers,
}, {
  maxAttempts: 3,
  baseDelay: 1000,
  retryCondition: (error) => {
    if (error instanceof Response) {
      return error.status >= 500 || error.status === 429;
    }
    return true;
  }
});
```

## 3. 性能优化措施

### 3.1 缓存策略

#### 多层缓存架构
1. **Cloudflare KV缓存**（服务端）:
   - 缓存时间: 60秒（资金流向数据）
   - 缓存时间: 300秒（最后更新时间）
   - 自动过期清理机制

2. **React Query缓存**（客户端）:
   - 数据新鲜时间: 5分钟
   - 缓存保持时间: 10分钟
   - 自动后台刷新

#### 缓存键策略
```typescript
// 股票数据缓存键
static getStockDataKey(code: string): string {
  return `stock_data:${code}`;
}

// 最后更新时间缓存键
static getLastUpdateKey(code: string): string {
  return `last_update:${code}`;
}
```

### 3.2 网络优化

#### 超时设置
- **默认超时**: 10秒
- **重试超时**: 递增（1s, 2s, 4s）
- **连接超时**: 5秒

#### 请求优化
- **Keep-Alive**: 启用HTTP连接复用
- **压缩**: 支持gzip压缩
- **DNS预解析**: 预解析东方财富域名

### 3.3 数据处理优化

#### 数据验证和清洗
```typescript
// 数据验证
if (!validateEastmoneyResponse(apiData)) {
  return { success: false, message: '响应数据格式无效' };
}

// 数据处理
const processedData = this.processFlowData(apiData);
```

#### 内存优化
- **数据分页**: 限制单次获取数据量（默认240条）
- **垃圾回收**: 及时清理过期缓存
- **对象池**: 复用数据处理对象

## 4. 错误处理机制

### 4.1 错误分类处理

#### API错误处理
```typescript
// 检查API返回码
if (apiData.rc !== 0) {
  return {
    success: false,
    message: `API返回错误码: ${apiData.rc}`,
    timestamp: new Date().toISOString(),
  };
}
```

#### 网络错误处理
- **连接超时**: 自动重试
- **DNS解析失败**: 记录错误，不重试
- **HTTP错误**: 根据状态码决定是否重试

### 4.2 错误恢复策略

#### 降级处理
1. **缓存降级**: API失败时返回缓存数据
2. **部分失败**: 批量请求中部分失败时返回成功的数据
3. **默认值**: 关键数据缺失时使用默认值

#### 错误监控
- **错误日志**: 详细记录错误信息和上下文
- **错误统计**: 统计错误率和错误类型
- **告警机制**: 错误率超阈值时触发告警

## 5. 监控和统计

### 5.1 性能指标

#### 缓存统计
```typescript
interface CacheStatistics {
  operations: {
    gets: number;      // 获取次数
    sets: number;      // 设置次数
    hits: number;      // 命中次数
    misses: number;    // 未命中次数
    deletes: number;   // 删除次数
  };
}
```

#### API调用统计
- **成功率**: 成功请求/总请求数
- **平均响应时间**: 请求响应时间统计
- **错误分布**: 各类错误的分布情况

### 5.2 实时监控

#### 健康检查
- **API可用性**: 定期检查API服务状态
- **缓存状态**: 监控缓存命中率和容量
- **系统资源**: 监控内存和CPU使用情况

## 6. 部署和配置

### 6.1 环境配置

#### Cloudflare Workers配置
```toml
# wrangler.toml
name = "stock-flow-api"
main = "src/index.ts"
compatibility_date = "2023-12-01"

[[kv_namespaces]]
binding = "STOCK_CACHE"
id = "your-kv-namespace-id"
```

#### 环境变量
- `API_BASE_URL`: API基础URL
- `CACHE_TTL`: 缓存过期时间
- `RATE_LIMIT`: 频率限制配置

### 6.2 监控配置

#### 日志级别
- `DEBUG`: 详细调试信息
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

## 7. 代码实现细节

### 7.1 核心服务类

#### EastmoneyApiService类
**文件位置**: `workers/src/services/eastmoneyApi.ts`

<augment_code_snippet path="workers/src/services/eastmoneyApi.ts" mode="EXCERPT">
````typescript
export class EastmoneyApiService {
  private readonly baseUrl = 'https://push2.eastmoney.com';
  private readonly rateLimiter: RateLimiter;

  constructor() {
    // 限制每分钟最多60个请求
    this.rateLimiter = new RateLimiter(60, 60000);
  }

  async getStockFlowData(stockCode: string, limit: number = 240): Promise<ApiResponse<any>> {
    // 数据验证
    const cleanCode = sanitizeStockCode(stockCode);
    if (!validateStockCode(cleanCode)) {
      return { success: false, message: '无效的股票代码' };
    }

    // 等待频率限制
    await this.rateLimiter.waitForSlot();

    // 构建请求URL和发送请求
    const url = this.buildFlowDataUrl(cleanCode, limit);
    const response = await fetchWithRetry(url, options, retryConfig);

    // 处理响应数据
    const processedData = this.processFlowData(apiData);
    return { success: true, data: processedData };
  }
}
````
</augment_code_snippet>

#### 数据处理器
**核心功能**: 将东方财富API返回的原始数据转换为标准格式

<augment_code_snippet path="workers/src/services/eastmoneyApi.ts" mode="EXCERPT">
````typescript
private processFlowData(apiData: EastmoneyApiResponse): any {
  const klines = apiData.data?.klines?.map((kline: string) => {
    const fields = kline.split(',');
    return {
      date: fields[0],           // 日期时间
      mainNetInflow: parseFloat(fields[1]) || 0,    // 主力净流入
      superLargeInflow: parseFloat(fields[2]) || 0,  // 超大单流入
      superLargeOutflow: parseFloat(fields[3]) || 0, // 超大单流出
      largeInflow: parseFloat(fields[4]) || 0,       // 大单流入
      largeOutflow: parseFloat(fields[5]) || 0,      // 大单流出
      mediumInflow: parseFloat(fields[6]) || 0,      // 中单流入
      mediumOutflow: parseFloat(fields[7]) || 0,     // 中单流出
      smallInflow: parseFloat(fields[8]) || 0,       // 小单流入
      smallOutflow: parseFloat(fields[9]) || 0,      // 小单流出
    };
  }) || [];

  return {
    klines,
    summary: this.calculateSummary(klines),
    metadata: {
      stockCode: apiData.data?.code,
      updateTime: new Date().toISOString(),
      dataCount: klines.length
    }
  };
}
````
</augment_code_snippet>

### 7.2 重试和频率限制实现

#### RateLimiter类
**文件位置**: `workers/src/utils/retry.ts`

<augment_code_snippet path="workers/src/utils/retry.ts" mode="EXCERPT">
````typescript
export class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number, windowMs: number) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  canMakeRequest(): boolean {
    const now = Date.now();
    // 清理过期的请求记录
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    return this.requests.length < this.maxRequests;
  }

  async waitForSlot(): Promise<void> {
    while (!this.canMakeRequest()) {
      await delay(100); // 等待100ms后重新检查
    }
    this.recordRequest();
  }
}
````
</augment_code_snippet>

#### 重试机制实现
<augment_code_snippet path="workers/src/utils/retry.ts" mode="EXCERPT">
````typescript
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryConfig: Partial<RetryConfig> = {}
): Promise<Response> {
  const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  return withRetry(async () => {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw response;
    }
    return response;
  }, config);
}

// 指数退避延迟计算
function calculateBackoffDelay(attempt: number, config: RetryConfig): number {
  const exponentialDelay = config.baseDelay * Math.pow(2, attempt);
  const jitter = Math.random() * 0.1 * exponentialDelay;
  return Math.min(exponentialDelay + jitter, config.maxDelay);
}
````
</augment_code_snippet>

### 7.3 缓存服务实现

#### CacheService类
**文件位置**: `workers/src/services/cache.ts`

<augment_code_snippet path="workers/src/services/cache.ts" mode="EXCERPT">
````typescript
export class CacheService {
  private kv: KVNamespace;
  private stats: CacheStatistics['operations'];

  async get<T>(key: CacheKey): Promise<T | null> {
    try {
      const cached = await this.kv.get(key);
      if (!cached) {
        this.stats.misses++;
        return null;
      }

      const entry = JSON.parse(cached) as CacheEntry<T>;

      // 检查是否过期
      if (entry.metadata && new Date(entry.metadata.expiresAt) < new Date()) {
        this.stats.misses++;
        await this.delete(key);
        return null;
      }

      this.stats.hits++;
      return entry.data !== undefined ? entry.data : (entry as unknown as T);
    } catch (error) {
      this.stats.misses++;
      return null;
    }
  }

  async set<T>(key: CacheKey, value: T, ttl: number = 60): Promise<void> {
    const entry: CacheEntry<T> = {
      data: value,
      metadata: {
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + ttl * 1000).toISOString(),
        ttl,
        version: '1.0'
      }
    };

    await this.kv.put(key, JSON.stringify(entry), { expirationTtl: ttl });
  }
}
````
</augment_code_snippet>

### 7.4 前端数据获取Hook

#### useBatchStockData Hook
**文件位置**: `src/hooks/useStockData.ts`

<augment_code_snippet path="src/hooks/useStockData.ts" mode="EXCERPT">
````typescript
export const useBatchStockData = (
  codes: string[],
  limit: number = 240,
  options: UseQueryOptions = {}
) => {
  return useQuery(
    ['batch-stock-data', codes.join(','), limit],
    async () => {
      if (codes.length === 0) return { results: {}, errors: {} };

      // 分批处理，避免单次请求过多股票
      const BATCH_SIZE = 20;
      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      for (let i = 0; i < codes.length; i += BATCH_SIZE) {
        const batchCodes = codes.slice(i, i + BATCH_SIZE);

        try {
          const batchResult = await stockDataApi.getBatchStockData(batchCodes, limit);

          if (batchResult.success && batchResult.data) {
            Object.assign(results, batchResult.data.results || {});
            Object.assign(errors, batchResult.data.errors || {});
          }
        } catch (error) {
          // 批次失败时，将该批次所有股票标记为错误
          batchCodes.forEach(code => {
            errors[code] = error instanceof Error ? error.message : '获取失败';
          });
        }
      }

      return { results, errors };
    },
    {
      refetchInterval: 60000, // 60秒自动刷新
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      ...options
    }
  );
};
````
</augment_code_snippet>

### 7.5 实时监控组件

#### StockMonitorPanel组件
**文件位置**: `src/components/StockMonitor/StockMonitorPanel.tsx`

<augment_code_snippet path="src/components/StockMonitor/StockMonitorPanel.tsx" mode="EXCERPT">
````typescript
export const StockMonitorPanel: React.FC<StockMonitorPanelProps> = ({
  compact = false,
  maxItems = 20,
  onStockClick,
}) => {
  const { stocks } = useStockList();
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据，每分钟自动刷新
  const {
    results,
    isLoading,
    successCount,
    errorCount
  } = useBatchStockData(stockCodes, 20, {
    refetchInterval: 60000, // 60秒自动刷新
    enabled: stockCodes.length > 0
  });

  // 数据处理和排序逻辑
  const processedStocks = useMemo(() => {
    return stocks.map(stock => {
      const data = results[stock.code];
      return {
        ...stock,
        data,
        vPattern: data ? detectVPattern(data.klines) : null,
        flowScore: data ? calculateFlowScore(data.summary) : 0
      };
    }).sort((a, b) => {
      // 按V字型模式和资金流向评分排序
      if (a.vPattern && !b.vPattern) return -1;
      if (!a.vPattern && b.vPattern) return 1;
      return b.flowScore - a.flowScore;
    });
  }, [stocks, results]);

  return (
    <div className="space-y-2">
      {processedStocks.slice(0, maxItems).map(stock => (
        <StockMonitorItem
          key={stock.code}
          stock={stock}
          compact={compact}
          onClick={() => onStockClick?.(stock)}
        />
      ))}
    </div>
  );
};
````
</augment_code_snippet>

## 8. 关键配置参数

### 8.1 API调用配置

#### 频率限制参数
```typescript
// 东方财富API频率限制
const RATE_LIMIT_CONFIG = {
  maxRequests: 60,        // 每分钟最大请求数
  windowMs: 60000,        // 时间窗口（毫秒）
  checkInterval: 100      // 检查间隔（毫秒）
};

// 批量请求配置
const BATCH_CONFIG = {
  batchSize: 20,          // 每批处理股票数量
  concurrentLimit: 10,    // 并发请求限制
  delayBetweenRequests: 200, // 请求间延迟（毫秒）
  maxRetries: 3           // 最大重试次数
};
```

#### 重试策略配置
```typescript
const RETRY_CONFIG = {
  maxAttempts: 3,         // 最大重试次数
  baseDelay: 1000,        // 基础延迟（毫秒）
  maxDelay: 30000,        // 最大延迟（毫秒）
  backoffFactor: 2,       // 退避因子
  jitterFactor: 0.1       // 抖动因子
};
```

### 8.2 缓存配置

#### 缓存时间设置
```typescript
const CACHE_TTL = {
  stockData: 60,          // 股票数据缓存60秒
  lastUpdate: 300,        // 更新时间缓存5分钟
  stockQuote: 30,         // 行情数据缓存30秒
  batchData: 60           // 批量数据缓存60秒
};

// React Query缓存配置
const QUERY_CONFIG = {
  staleTime: 5 * 60 * 1000,    // 数据新鲜时间5分钟
  cacheTime: 10 * 60 * 1000,   // 缓存保持时间10分钟
  refetchInterval: 60000        // 自动刷新间隔60秒
};
```

### 8.3 数据处理配置

#### 股票代码验证规则
```typescript
const STOCK_CODE_RULES = {
  // 上海证券交易所：6开头，6位数字
  shanghai: /^6\d{5}$/,
  // 深圳证券交易所：0、2、3开头，6位数字
  shenzhen: /^[023]\d{5}$/,
  // 创业板：3开头
  chinext: /^3\d{5}$/,
  // 科创板：688开头
  star: /^688\d{3}$/
};

// 市场代码映射
const MARKET_CODE_MAP = {
  shanghai: 1,    // 上海证券交易所
  shenzhen: 0     // 深圳证券交易所
};
```

## 9. 性能监控指标

### 9.1 API性能指标

#### 响应时间监控
```typescript
interface ApiPerformanceMetrics {
  averageResponseTime: number;    // 平均响应时间（毫秒）
  p95ResponseTime: number;        // 95%分位响应时间
  p99ResponseTime: number;        // 99%分位响应时间
  successRate: number;            // 成功率（百分比）
  errorRate: number;              // 错误率（百分比）
  requestsPerMinute: number;      // 每分钟请求数
}
```

#### 错误统计
```typescript
interface ErrorStatistics {
  networkErrors: number;          // 网络错误次数
  timeoutErrors: number;          // 超时错误次数
  apiErrors: number;              // API错误次数
  parseErrors: number;            // 解析错误次数
  validationErrors: number;       // 验证错误次数
}
```

### 9.2 缓存性能指标

#### 缓存命中率
```typescript
interface CacheMetrics {
  hitRate: number;                // 命中率（百分比）
  missRate: number;               // 未命中率（百分比）
  totalRequests: number;          // 总请求数
  cacheSize: number;              // 缓存大小（字节）
  evictionCount: number;          // 淘汰次数
}
```

## 10. 故障排查指南

### 10.1 常见问题及解决方案

#### API调用失败
**问题现象**: 返回HTTP 429或5xx错误
**排查步骤**:
1. 检查频率限制状态：`rateLimiter.canMakeRequest()`
2. 查看错误日志中的具体错误码
3. 验证请求头是否正确设置
4. 检查网络连接状态

**解决方案**:
```typescript
// 检查频率限制状态
const status = apiService.getServiceStatus();
if (!status.rateLimitStatus.canMakeRequest) {
  console.log('频率限制中，下次可用时间：',
    new Date(status.rateLimitStatus.nextAvailableTime));
}
```

#### 数据解析错误
**问题现象**: 返回"数据解析失败"错误
**排查步骤**:
1. 检查API响应格式是否发生变化
2. 验证JSON解析是否正常
3. 查看数据验证规则是否过时

**解决方案**:
```typescript
// 增强错误日志
try {
  const apiData = JSON.parse(responseText);
} catch (error) {
  console.error('JSON解析失败:', {
    error: error.message,
    responseText: responseText.substring(0, 200) // 只记录前200字符
  });
}
```

#### 缓存问题
**问题现象**: 数据更新不及时或缓存命中率低
**排查步骤**:
1. 检查缓存TTL设置是否合理
2. 验证缓存键是否正确生成
3. 查看缓存统计信息

**解决方案**:
```typescript
// 监控缓存状态
const cacheStats = cacheService.getStatistics();
console.log('缓存命中率:', cacheService.getHitRate());
console.log('缓存统计:', cacheStats);
```

### 10.2 监控告警设置

#### 关键指标阈值
```typescript
const ALERT_THRESHOLDS = {
  errorRate: 5,           // 错误率超过5%告警
  responseTime: 5000,     // 响应时间超过5秒告警
  cacheHitRate: 80,       // 缓存命中率低于80%告警
  apiSuccessRate: 95      // API成功率低于95%告警
};
```

## 11. 部署检查清单

### 11.1 环境配置检查
- [ ] Cloudflare Workers配置正确
- [ ] KV命名空间绑定正确
- [ ] 环境变量设置完整
- [ ] 域名和路由配置正确

### 11.2 功能测试检查
- [ ] 单股票数据获取正常
- [ ] 批量股票数据获取正常
- [ ] 缓存机制工作正常
- [ ] 错误处理机制有效
- [ ] 频率限制功能正常

### 11.3 性能测试检查
- [ ] API响应时间在可接受范围内
- [ ] 并发请求处理正常
- [ ] 内存使用量稳定
- [ ] 缓存命中率达到预期

---

*文档版本: v1.0*
*最后更新: 2025-01-02*
*维护者: 开发团队*
