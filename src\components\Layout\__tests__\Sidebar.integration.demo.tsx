import React, { useState } from 'react';
import { Sidebar } from '../Sidebar';

/**
 * 侧边栏集成演示组件
 * 展示集成监控面板后的侧边栏功能
 */
export const SidebarIntegrationDemo: React.FC = () => {
  const [selectedStock, setSelectedStock] = useState<string | null>('000001');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const handleStockSelect = (code: string) => {
    setSelectedStock(code);
    console.log('选中股票:', code);
  };

  const handleCloseMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        {/* 桌面端侧边栏 */}
        <div className="hidden lg:block w-80 bg-white shadow-lg">
          <Sidebar
            selectedStock={selectedStock}
            onStockSelect={handleStockSelect}
            isOpen={true}
          />
        </div>

        {/* 移动端侧边栏覆盖层 */}
        {isMobileSidebarOpen && (
          <div className="lg:hidden fixed inset-0 z-50">
            <div className="absolute inset-0 bg-black bg-opacity-50" onClick={handleCloseMobileSidebar}></div>
            <div className="relative w-80 bg-white h-full shadow-xl">
              <Sidebar
                selectedStock={selectedStock}
                onStockSelect={handleStockSelect}
                isOpen={isMobileSidebarOpen}
                onClose={handleCloseMobileSidebar}
              />
            </div>
          </div>
        )}

        {/* 主内容区域 */}
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold mb-6">侧边栏监控面板集成演示</h1>
            
            {/* 移动端打开侧边栏按钮 */}
            <div className="lg:hidden mb-6">
              <button
                onClick={() => setIsMobileSidebarOpen(true)}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              >
                打开侧边栏
              </button>
            </div>

            {/* 功能说明 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 shadow">
                <h3 className="text-lg font-semibold mb-4 text-green-600">✅ 新增功能</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• 实时监控面板集成到侧边栏</li>
                  <li>• V字型股票自动识别和高亮</li>
                  <li>• 每分钟自动刷新数据</li>
                  <li>• 展开/收起功能节省空间</li>
                  <li>• 点击股票自动选中</li>
                  <li>• 紧凑模式适配侧边栏</li>
                  <li>• 移动端响应式设计</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow">
                <h3 className="text-lg font-semibold mb-4 text-blue-600">🔧 保留功能</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• 股票添加和删除功能</li>
                  <li>• 股票名称自动获取</li>
                  <li>• 当前选中股票显示</li>
                  <li>• 错误状态提示</li>
                  <li>• 原有的交互逻辑</li>
                  <li>• 移动端侧边栏开关</li>
                  <li>• 整体布局和样式</li>
                </ul>
              </div>
            </div>

            {/* 当前状态显示 */}
            <div className="bg-white rounded-lg p-6 shadow mb-6">
              <h3 className="text-lg font-semibold mb-4">当前状态</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm text-blue-600 mb-1">选中股票</div>
                  <div className="text-lg font-bold text-blue-900">
                    {selectedStock || '未选中'}
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-sm text-green-600 mb-1">侧边栏状态</div>
                  <div className="text-lg font-bold text-green-900">
                    {isMobileSidebarOpen ? '已打开' : '已关闭'}
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-sm text-purple-600 mb-1">监控功能</div>
                  <div className="text-lg font-bold text-purple-900">
                    已集成
                  </div>
                </div>
              </div>
            </div>

            {/* 使用说明 */}
            <div className="bg-white rounded-lg p-6 shadow">
              <h3 className="text-lg font-semibold mb-4">使用说明</h3>
              <div className="space-y-4 text-sm text-gray-700">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">1. 监控面板操作</h4>
                  <p>• 点击侧边栏中的展开/收起按钮控制监控面板显示</p>
                  <p>• 点击监控面板中的股票项可以选中该股票</p>
                  <p>• V字型模式的股票会自动高亮显示</p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">2. 股票管理</h4>
                  <p>• 在侧边栏上方的股票管理区域添加或删除股票</p>
                  <p>• 添加股票时会自动获取真实股票名称</p>
                  <p>• 监控面板会自动更新显示新添加的股票</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">3. 响应式设计</h4>
                  <p>• 桌面端：侧边栏固定显示在左侧</p>
                  <p>• 移动端：点击按钮打开覆盖层侧边栏</p>
                  <p>• 监控面板在不同屏幕尺寸下自动适配</p>
                </div>
              </div>
            </div>

            {/* 技术特性 */}
            <div className="mt-8 bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">技术特性</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">性能优化</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• React Query 智能缓存</li>
                    <li>• 组件级状态管理</li>
                    <li>• 虚拟滚动支持</li>
                    <li>• 按需渲染优化</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">用户体验</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 平滑动画过渡</li>
                    <li>• 加载状态提示</li>
                    <li>• 错误处理机制</li>
                    <li>• 无障碍访问支持</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 如果在浏览器环境中，将演示组件挂载到window对象
if (typeof window !== 'undefined') {
  (window as any).SidebarIntegrationDemo = SidebarIntegrationDemo;
  console.log('🎛️ 侧边栏集成演示已加载，在控制台运行 SidebarIntegrationDemo 查看效果');
}

export default SidebarIntegrationDemo;
