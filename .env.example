# 前端环境变量示例
# 复制此文件为 .env.local 并填入实际值

# API基础URL - 指向你的Workers部署地址
VITE_API_BASE_URL=http://localhost:8787

# 应用标题
VITE_APP_TITLE=股票资金流向监控

# 自动刷新间隔（毫秒）
VITE_REFRESH_INTERVAL=60000

# 是否启用开发工具
VITE_ENABLE_DEVTOOLS=true

# Cloudflare Workers环境变量
# 这些变量在wrangler.toml中配置，此处仅作说明
# ENVIRONMENT=development
# API_BASE_URL=https://push2.eastmoney.com
# CRON_ENABLED=true
# CACHE_TTL=60
# BATCH_SIZE=10
# MAX_RETRIES=3
# LOG_LEVEL=info
